import { Caption, CaptionStyle } from "../types";
import { ASS_CONFIG } from "../config";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

// 提取常量
const CONVERSION_CONSTANTS = {
  CHAR_SPACING_RATIO: 0.5,
  SHADOW_DISTANCE_RATIO: 0.5,
  BLUR_RATIO: 0.33,
  FRONTEND_TEXTBOX_WIDTH_RATIO: 0.9,
  FRONTEND_TEXTBOX_PADDING: 10,
  DEFAULT_ALPHA_VALUE: 160,
  SCALE_VALUES: { X: 100, Y: 100 },
  BORDER_STYLE: 1,
  ENCODING: 1,
} as const;

// ASS对齐映射
const ASS_ALIGNMENT_MAP = {
  "top-left": 7,
  "top-center": 8,
  "top-right": 9,
  "center-left": 4,
  "center-center": 5,
  "center-right": 6,
  "bottom-left": 1,
  "bottom-center": 2,
  "bottom-right": 3,
} as const;

/**
 * ASS字幕工具类
 * 提供高级的ASS字幕生成和处理功能
 */
export class ASSSubtitleUtils {
  /**
   * 转换颜色格式：从 #RRGGBB 到 &HBBGGRR& (ASS格式)
   */
  static convertColorToASS(hexColor: string): string {
    if (!hexColor || hexColor === "transparent") {
      return ASS_CONFIG.COLORS.TRANSPARENT;
    }

    const hex = hexColor.replace("#", "");
    if (hex.length !== 6) {
      return ASS_CONFIG.COLORS.WHITE;
    }

    const r = hex.substring(0, 2);
    const g = hex.substring(2, 4);
    const b = hex.substring(4, 6);
    return `&H${b}${g}${r}&`;
  }

  /**
   * 转换对齐方式到ASS格式
   */
  static getASSAlignment(textAlign: string): number {
    const alignmentMap: Record<string, number> = {
      left: ASS_CONFIG.ALIGNMENT.LEFT,
      right: ASS_CONFIG.ALIGNMENT.RIGHT,
      center: ASS_CONFIG.ALIGNMENT.CENTER,
    };

    return alignmentMap[textAlign] || ASS_CONFIG.ALIGNMENT.CENTER;
  }

  /**
   * 转换时间格式到ASS格式
   */
  static convertTimeToASS(timeStr: string): string {
    const parts = timeStr.split(":");
    if (parts.length !== 3) {
      return timeStr + ".00";
    }

    const hours = parseInt(parts[0], 10);
    const minutes = parts[1];
    const seconds = parts[2];
    return `${hours}:${minutes}:${seconds}.00`;
  }

  /**
   * 转义ASS文本
   */
  static escapeASSText(text: string): string {
    if (!text) return "";

    return text
      .replace(/\\/g, "\\\\")
      .replace(/\{/g, "\\{")
      .replace(/\}/g, "\\}")
      .replace(/\n/g, "\\N");
  }

  /**
   * 计算边距
   */
  private static calculateMargins(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): { sideMargin: number; bottomMargin: number } {
    // 计算底部边距
    let bottomMargin = canvasHeight
      ? Math.round(canvasHeight * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;

    if (style.positionY !== undefined && canvasHeight) {
      bottomMargin = Math.max(0, bottomMargin - style.positionY);
    }

    // 计算侧边距
    let sideMargin = canvasWidth
      ? Math.round(canvasWidth * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;

    if (canvasWidth) {
      const effectiveFrontendTextAreaWidth = Math.max(
        0,
        canvasWidth * CONVERSION_CONSTANTS.FRONTEND_TEXTBOX_WIDTH_RATIO -
          2 * CONVERSION_CONSTANTS.FRONTEND_TEXTBOX_PADDING
      );

      sideMargin = Math.round(
        (canvasWidth - effectiveFrontendTextAreaWidth) / 2
      );
    }

    return { sideMargin, bottomMargin };
  }

  /**
   * 生成ASS样式字符串
   */
  static generateASSStyle(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    const isBold = style.styles.includes("bold") || style.fontWeight >= 700;
    const isItalic = style.styles.includes("italic");
    const hasUnderline =
      style.styles.includes("underline") || style.styles.includes("underlined");
    const isStrikethrough =
      style.styles.includes("strikethrough") ||
      style.styles.includes("strikeOut");

    // 使用 -1 来实现连续下划线效果，这比普通下划线(1)效果更好
    const underlineValue = hasUnderline ? -1 : 0;

    const { sideMargin, bottomMargin } = this.calculateMargins(
      style,
      canvasWidth,
      canvasHeight
    );

    const styleComponents = [
      style.fontFamily || ASS_CONFIG.DEFAULT_STYLE.fontFamily,
      style.fontSize || ASS_CONFIG.DEFAULT_STYLE.fontSize,
      this.convertColorToASS(style.fontColor),
      this.convertColorToASS(style.fontColor),
      this.convertColorToASS(style.strokeColor),
      this.convertColorToASS(style.backgroundColor),
      isBold ? 1 : 0,
      isItalic ? 1 : 0,
      underlineValue, // 使用 -1 实现连续下划线效果
      isStrikethrough ? 1 : 0, // 修复：正确处理删除线
      CONVERSION_CONSTANTS.SCALE_VALUES.X,
      CONVERSION_CONSTANTS.SCALE_VALUES.Y,
      Math.round(
        (style.charSpacing || 0) * CONVERSION_CONSTANTS.CHAR_SPACING_RATIO
      ),
      0, // Angle
      CONVERSION_CONSTANTS.BORDER_STYLE,
      Math.max(0, style.strokeWidth || 0),
      Math.max(0, style.shadowBlur || 0),
      this.getASSAlignment(style.textAlign),
      sideMargin, // MarginL
      sideMargin, // MarginR
      bottomMargin, // MarginV
      CONVERSION_CONSTANTS.ENCODING,
    ];

    console.log(
      `ASS样式生成 - 下划线参数: ${underlineValue} (${
        hasUnderline ? "启用连续下划线" : "无下划线"
      })`
    );

    return styleComponents.join(",");
  }

  /**
   * 获取ASS对齐值
   */
  private static getASSAlignmentValue(
    originX?: string,
    originY?: string
  ): number {
    const vertical = originY || "bottom";
    const horizontal = originX || "center";
    const key = `${vertical}-${horizontal}` as keyof typeof ASS_ALIGNMENT_MAP;

    return ASS_ALIGNMENT_MAP[key] || ASS_ALIGNMENT_MAP["bottom-center"];
  }

  /**
   * 计算位置坐标
   */
  private static calculatePosition(
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): { xPos: number; yPos: number } {
    // 计算水平位置
    let xPos = canvasWidth / 2; // 默认居中
    if (style.originX === "left") {
      xPos = 0;
    } else if (style.originX === "right") {
      xPos = canvasWidth;
    }
    if (style.positionX !== undefined) {
      xPos += style.positionX;
    }

    // 计算垂直位置
    let yPos: number;
    const defaultBottomMarginRatio = ASS_CONFIG.DEFAULT_MARGIN_RATIO;

    switch (style.originY) {
      case "top":
        yPos = 0;
        break;
      case "center":
        yPos = canvasHeight / 2;
        break;
      case "bottom":
      default:
        yPos = canvasHeight - canvasHeight * defaultBottomMarginRatio;
        break;
    }

    if (style.positionY !== undefined) {
      yPos += style.positionY;
    }

    return { xPos: Math.round(xPos), yPos: Math.round(yPos) };
  }

  /**
   * 生成位置标签
   */
  private static generatePositionTags(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (
      (!style.positionX && !style.positionY) ||
      !canvasWidth ||
      !canvasHeight
    ) {
      return "";
    }

    const { xPos, yPos } = this.calculatePosition(
      style,
      canvasWidth,
      canvasHeight
    );
    const alignmentValue = this.getASSAlignmentValue(
      style.originX,
      style.originY
    );

    return `{\\\\an${alignmentValue}\\\\pos(${xPos},${yPos})}`;
  }

  /**
   * 生成渐变标签
   */
  private static generateGradientTags(style: CaptionStyle): string {
    if (
      !style.useGradient ||
      !style.gradientColors ||
      style.gradientColors.length < 2
    ) {
      return "";
    }

    const color1 = this.convertColorToASS(style.gradientColors[0]);
    const color2 = this.convertColorToASS(style.gradientColors[1]);
    return `{\\1c${color1}\\3c${color2}}`;
  }

  /**
   * 生成字符间距标签
   */
  private static generateCharSpacingTags(style: CaptionStyle): string {
    if (!style.charSpacing || style.charSpacing === 0) {
      return "";
    }

    const assCharSpacing = Math.round(
      style.charSpacing * CONVERSION_CONSTANTS.CHAR_SPACING_RATIO
    );
    return `{\\fsp${assCharSpacing}}`;
  }

  /**
   * 生成阴影标签
   */
  private static generateShadowTags(style: CaptionStyle): string {
    let shadowTags = "";

    // 阴影距离和模糊
    if (style.shadowBlur > 0) {
      const shadowDistance = Math.max(
        1,
        Math.round(
          style.shadowBlur * CONVERSION_CONSTANTS.SHADOW_DISTANCE_RATIO
        )
      );
      shadowTags += `\\shad${shadowDistance}`;

      const blurValue = Math.max(
        0.5,
        style.shadowBlur * CONVERSION_CONSTANTS.BLUR_RATIO
      );
      shadowTags += `\\blur${blurValue.toFixed(1)}`;
    }

    // 阴影颜色
    if (style.shadowColor && style.shadowColor !== "transparent") {
      const shadowColor = this.convertColorToASS(style.shadowColor);
      shadowTags += `\\4c${shadowColor}`;

      const alphaValue = (255 - CONVERSION_CONSTANTS.DEFAULT_ALPHA_VALUE)
        .toString(16)
        .padStart(2, "0");
      shadowTags += `\\4a&H${alphaValue}&`;
    }

    // 阴影偏移
    if (style.shadowOffsetX !== 0 || style.shadowOffsetY !== 0) {
      if (style.shadowOffsetX) {
        shadowTags += `\\xshad${style.shadowOffsetX}`;
      }
      if (style.shadowOffsetY) {
        shadowTags += `\\yshad${style.shadowOffsetY}`;
      }
    }

    return shadowTags ? `{${shadowTags}}` : "";
  }

  /**
   * 处理单个字幕文本
   */
  private static processSubtitleText(
    caption: Caption,
    finalStyle: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    let escapedText = this.escapeASSText(caption.text);

    // 按优先级添加各种标签
    const positionTags = this.generatePositionTags(
      finalStyle,
      canvasWidth,
      canvasHeight
    );
    const gradientTags = this.generateGradientTags(finalStyle);
    const charSpacingTags = this.generateCharSpacingTags(finalStyle);
    const shadowTags = this.generateShadowTags(finalStyle);

    // 组合所有标签
    return (
      positionTags + gradientTags + charSpacingTags + shadowTags + escapedText
    );
  }

  /**
   * 生成ASS文件头部
   */
  private static generateASSHeader(
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    return `[Script Info]
Title: Generated Subtitles
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: None
PlayResX: ${canvasWidth || 1920}
PlayResY: ${canvasHeight || 1080}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
`;
  }

  /**
   * 生成完整的ASS文件内容
   */
  static generateASSContent(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!captions?.length) {
      throw new Error("字幕数据不能为空");
    }

    const defaultStyle: CaptionStyle = {
      ...ASS_CONFIG.DEFAULT_STYLE,
      styles: [...ASS_CONFIG.DEFAULT_STYLE.styles],
      gradientColors: [...ASS_CONFIG.DEFAULT_STYLE.gradientColors],
      fontSize: canvasHeight
        ? Math.max(
            ASS_CONFIG.MIN_FONT_SIZE,
            Math.round(canvasHeight * ASS_CONFIG.DEFAULT_FONT_SIZE_RATIO)
          )
        : ASS_CONFIG.DEFAULT_STYLE.fontSize,
    };

    const finalStyle: CaptionStyle = style
      ? { ...defaultStyle, ...style }
      : defaultStyle;

    // 生成ASS文件头部
    let assContent = this.generateASSHeader(canvasWidth, canvasHeight);

    // 添加样式定义
    assContent += `Style: Default,${this.generateASSStyle(
      finalStyle,
      canvasWidth,
      canvasHeight
    )}\n\n`;

    // 添加事件格式
    assContent += `[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n`;

    // 处理每个字幕
    captions.forEach((caption) => {
      try {
        const startTime = this.convertTimeToASS(caption.startTime);
        const endTime = this.convertTimeToASS(caption.endTime);
        const processedText = this.processSubtitleText(
          caption,
          finalStyle,
          canvasWidth,
          canvasHeight
        );

        assContent += `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,${processedText}\n`;
      } catch (error) {
        console.warn(`处理字幕时出错: ${caption.text}`, error);
      }
    });

    return assContent;
  }

  /**
   * 创建ASS字幕文件
   */
  static createASSFile(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    try {
      const assContent = this.generateASSContent(
        captions,
        style,
        canvasWidth,
        canvasHeight
      );

      const tempDir = os.tmpdir();
      const timestamp = new Date()
        .toISOString()
        .replace(/[:.]/g, "-")
        .replace("T", "_")
        .replace("Z", "");

      const subtitlePath = path.join(tempDir, `ass_subtitles_${timestamp}.ass`);

      fs.writeFileSync(subtitlePath, assContent, "utf8");
      console.log("Generated ASS subtitle file:", subtitlePath);

      return subtitlePath;
    } catch (error) {
      console.error("创建ASS文件失败:", error);
      throw new Error(
        `创建ASS文件失败: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * 验证ASS文件格式
   */
  static validateASSFile(filePath: string): boolean {
    try {
      if (!fs.existsSync(filePath)) {
        return false;
      }

      const content = fs.readFileSync(filePath, "utf8");
      const requiredSections = ["[Script Info]", "[V4+ Styles]", "[Events]"];

      return requiredSections.every((section) => content.includes(section));
    } catch (error) {
      console.error("验证ASS文件时出错:", error);
      return false;
    }
  }

  /**
   * 获取ASS文件的字幕数量
   */
  static getSubtitleCount(filePath: string): number {
    try {
      if (!fs.existsSync(filePath)) {
        return 0;
      }

      const content = fs.readFileSync(filePath, "utf8");
      const dialogueLines = content
        .split("\n")
        .filter((line) => line.trim().startsWith("Dialogue:"));

      return dialogueLines.length;
    } catch (error) {
      console.error("统计字幕数量时出错:", error);
      return 0;
    }
  }

  /**
   * 清理临时文件
   */
  static cleanupTempFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log("清理临时文件:", filePath);
      }
    } catch (error) {
      console.warn("清理临时文件失败:", error);
    }
  }
}
