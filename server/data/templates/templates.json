[{"id": "template-003", "name": "教育课程介绍", "description": "适合在线教育和课程介绍的模板，包含课程标题、讲师信息和课程大纲。", "category": "education", "tags": ["教育", "课程", "在线学习", "介绍"], "thumbnail": "/api/templates/thumbnails/education-course.jpg", "canvasState": {"width": 1920, "height": 1080, "backgroundColor": "#ffffff", "elements": [{"id": "course-title", "type": "text", "name": "课程标题", "properties": {"text": "课程名称", "fontSize": 54, "fontFamily": "<PERSON><PERSON>", "color": "#2c3e50", "fontWeight": "bold", "textAlign": "left"}, "placement": {"x": 100, "y": 150, "width": 800, "height": 80, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 0, "end": 8000}, "opacity": 1}, {"id": "instructor-info", "type": "text", "name": "讲师信息", "properties": {"text": "讲师：张老师 | 10年教学经验", "fontSize": 24, "fontFamily": "<PERSON><PERSON>", "color": "#7f8c8d", "textAlign": "left"}, "placement": {"x": 100, "y": 250, "width": 600, "height": 40, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 1000, "end": 8000}, "opacity": 1}, {"id": "course-outline", "type": "text", "name": "课程大纲", "properties": {"text": "• 第一章：基础知识\n• 第二章：进阶技巧\n• 第三章：实战项目\n• 第四章：总结回顾", "fontSize": 20, "fontFamily": "<PERSON><PERSON>", "color": "#34495e", "textAlign": "left"}, "placement": {"x": 100, "y": 350, "width": 500, "height": 200, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 2000, "end": 8000}, "opacity": 1}, {"id": "accent-bar", "type": "shape", "name": "强调条", "properties": {"shapeType": "rectangle", "fill": "#3498db", "stroke": "none", "strokeWidth": 0}, "placement": {"x": 50, "y": 150, "width": 8, "height": 400, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 500, "end": 8000}, "opacity": 1}], "animations": [{"id": "title-slide-in", "type": "slideIn", "targetId": "course-title", "duration": 1000, "group": "in", "properties": {"direction": "left", "useClipPath": false}}, {"id": "bar-grow", "type": "slideIn", "targetId": "accent-bar", "duration": 2000, "group": "in", "properties": {"direction": "up", "useClipPath": false}}], "captions": [], "duration": 8000}, "metadata": {"duration": 8000, "elementCount": 4, "hasVideo": false, "hasAudio": false, "hasText": true, "hasImages": false, "hasAnimations": true, "complexity": "medium", "aspectRatio": "16:9", "resolution": "1920x1080"}, "createdAt": "2024-01-01T02:00:00Z", "updatedAt": "2025-06-10T14:37:56.641Z", "isPublic": true, "authorName": "系统模板", "usageCount": 245, "rating": 4.7, "ratingCount": 45}]