import React, { useContext, useCallback, useRef } from "react";
import SliderWithInput from "./SliderWithInput";
import { Stack, Typography } from "@mui/material";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";

const PlaybackSpeedControl = ({ element }) => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();
  const [playbackSpeed, setPlaybackSpeed] = React.useState(
    element.playbackSpeed || 1
  );

  // 使用ref存储最新的速度值，避免在拖拽过程中频繁更新状态
  const speedRef = useRef(playbackSpeed);

  // 轻量级更新函数 - 只在拖拽过程中更新视频播放速度和UI状态
  const handlePlaybackSpeedChange = useCallback(
    (newValue) => {
      const speed = Array.isArray(newValue) ? newValue[0] : newValue;

      // 更新本地状态和ref
      setPlaybackSpeed(speed);
      speedRef.current = speed;

      // 只更新视频元素的播放速度，不触发Store更新
      if (element) {
        const videoElement = document.getElementById(
          element.properties.elementId
        ) as HTMLVideoElement;
        if (videoElement) {
          videoElement.playbackRate = speed;
        }
      }
    },
    [element]
  );

  // 重量级更新函数 - 只在拖拽结束时执行完整更新
  const handlePlaybackSpeedChangeCommitted = useCallback(
    (newValue) => {
      const speed = Array.isArray(newValue) ? newValue[0] : newValue;

      if (element) {
        const videoElement = document.getElementById(
          element.properties.elementId
        ) as HTMLVideoElement;
        if (videoElement) {
          // 确保视频元素的播放速度已更新
          videoElement.playbackRate = speed;

          // 创建元素的副本
          const updatedElement = { ...element };
          updatedElement.playbackSpeed = speed;
          updatedElement.duration = videoElement.duration;

          // 获取mediaStartTime（如果有）
          const mediaStartTime =
            (element.properties as any).mediaStartTime || 0;

          // 计算可用的媒体时长（考虑mediaStartTime）
          const availableMediaDuration = videoElement.duration - mediaStartTime;

          // 计算新的结束时间，考虑mediaStartTime
          const newEndTime =
            element.timeFrame.start + (availableMediaDuration * 1000) / speed;

          // 批量更新以减少重渲染
          // 1. 更新元素的其他属性
          updatedElement.timeFrame = {
            start: element.timeFrame.start,
            end: newEndTime,
          };

          // 2. 使用Store的方法更新元素
          store.updateEditorElement(updatedElement);

          // 3. 更新时间线
          store.updateEditorElementTimeFrame(
            element,
            {
              start: element.timeFrame.start,
              end: newEndTime,
            },
            true // 标记为拖拽结束，触发完整更新
          );

          console.log(
            `播放速度已更新: ${speed}x, 新的duration: ${(
              availableMediaDuration / speed
            ).toFixed(2)}秒, mediaStartTime: ${mediaStartTime}秒`
          );
        }
      }
    },
    [element, store]
  );

  // 使用useMemo计算当前duration，避免不必要的重新计算
  const currentDuration = React.useMemo(() => {
    if (!element || !element.duration) return 0;

    // 获取mediaStartTime（如果有）
    const mediaStartTime = (element.properties as any).mediaStartTime || 0;

    // 计算可用的媒体时长（考虑mediaStartTime）
    const availableDuration = element.duration - mediaStartTime;

    // 返回考虑播放速度后的可用时长
    return (availableDuration / playbackSpeed).toFixed(1);
  }, [element, playbackSpeed]);

  return (
    <Stack spacing={2} sx={{ m: 1 }}>
      <SliderWithInput
        label={t("playback_speed")}
        value={playbackSpeed}
        min={0.1}
        max={8}
        step={0.1}
        onChange={handlePlaybackSpeedChange}
        onChangeCommitted={handlePlaybackSpeedChangeCommitted}
      />
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        spacing={0.5}
      >
        <Typography
          variant="body2"
          sx={{
            color: "text.secondary",
          }}
        >
          {t("duration")}:
        </Typography>
        <Typography variant="body2">{currentDuration}</Typography>
      </Stack>
    </Stack>
  );
};

export default PlaybackSpeedControl;
