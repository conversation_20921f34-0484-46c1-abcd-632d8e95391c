import React, { useState } from "react";
import {
  Box,
  <PERSON>lider,
  <PERSON>Field,
  Stack,
  Typography,
  Tooltip,
  alpha,
} from "@mui/material";

interface SliderWithInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  onChangeCommitted?: (value: number) => void;
  min: number;
  max: number;
  step?: number;
  width?: string;
  textFieldWidth?: string;
  unit?: string;
}

const SliderWithInput = ({
  label,
  value,
  onChange,
  onChangeCommitted,
  min,
  max,
  step = 1,
  textFieldWidth = "40px",
  unit,
}: SliderWithInputProps) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [inputValue, setInputValue] = useState<string>(
    step < 1 ? value.toFixed(2) : value.toString()
  );

  const handleSliderChange = (e: any, newValue: number | number[]) => {
    const numValue = Number(newValue);
    onChange(numValue);
    setInputValue(step < 1 ? numValue.toFixed(2) : numValue.toString());
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
  };

  const handleBlur = () => {
    let numValue = parseFloat(inputValue);

    // 处理无效输入
    if (isNaN(numValue)) {
      numValue = value;
    }

    // 限制在最小值和最大值范围内
    numValue = Math.max(min, Math.min(max, numValue));

    onChange(numValue);
    setInputValue(step < 1 ? numValue.toFixed(2) : numValue.toString());

    if (onChangeCommitted) {
      onChangeCommitted(numValue);
    }
  };

  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      spacing={1}
      sx={{
        py: 0.5,
        "&:hover": {
          bgcolor: (theme) => alpha(theme.palette.primary.main, 0.05),
          borderRadius: 1,
        },
        transition: "background-color 0.2s ease",
      }}
    >
      <Typography
        variant="body2"
        sx={{
          color: "text.secondary",
          fontWeight: 500,
          minWidth: "30%",
        }}
      >
        {label}
      </Typography>
      <Stack
        direction="row"
        spacing={1}
        alignItems="center"
        sx={{ flexGrow: 1 }}
      >
        <Box sx={{ flexGrow: 1, px: 0.5 }}>
          <Tooltip
            open={showTooltip}
            title={`${value}${unit || ""}`}
            arrow
            placement="top"
          >
            <Slider
              size="small"
              value={value}
              onChange={handleSliderChange}
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              onChangeCommitted={
                onChangeCommitted
                  ? (e, newValue) => onChangeCommitted(Number(newValue))
                  : undefined
              }
              min={min}
              max={max}
              step={step}
              sx={{
                "& .MuiSlider-thumb": {
                  "&:hover, &.Mui-active": {
                    boxShadow: (theme) =>
                      `0 0 0 8px ${alpha(theme.palette.primary.main, 0.16)}`,
                  },
                  transition: "box-shadow 0.2s",
                },
                "& .MuiSlider-track": {
                  transition: "width 0.2s ease",
                },
              }}
            />
          </Tooltip>
        </Box>
        <TextField
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleBlur();
            }
          }}
          size="small"
          sx={{
            width: textFieldWidth,
            "& .MuiOutlinedInput-root": {
              height: "32px",
              borderRadius: 1,
              "& input": {
                padding: "4px 8px",
                fontSize: "0.7rem",
                textAlign: "left",
              },
              "&:hover fieldset": {
                borderColor: (theme) => theme.palette.primary.main,
              },
            },
          }}
          InputProps={{
            endAdornment: unit ? (
              <Typography
                variant="caption"
                sx={{ color: "text.secondary", mr: 0.5 }}
              >
                {unit}
              </Typography>
            ) : null,
          }}
        />
      </Stack>
    </Stack>
  );
};

export default SliderWithInput;
