import { fabric } from "fabric";
import {
  ROTATE_SVG,
  ROTATE_SVG_ACTIVE,
  ROTATE_CURSOR,
  COPY_SVG,
  DEL_SVG,
  COPY_SVG_ACTIVE,
  DEL_SVG_ACTIVE,
} from "../../../assets/icon";

import { initRectControl } from "./rect";
import { initLineControl } from "./fline";
import { initFTextControl } from "./ftext";

// Create a type for control configurations
type ControlConfig = {
  x: number;
  y: number;
  offsetX?: number;
  offsetY?: number;
  render?: Function;
  cursorStyle?: string;
  mouseUpHandler?: Function;
};

// Consolidate SVG image creation
const createSvgImage = (src: string): HTMLImageElement => {
  const img = document.createElement("img");
  img.src = src;
  return img;
};

const ROTATE_IMG = createSvgImage(ROTATE_SVG);
const ROTATE_IMG_ACTIVE = createSvgImage(ROTATE_SVG_ACTIVE);
const COPY_IMG = createSvgImage(COPY_SVG);
const COPY_IMG_ACTIVE = createSvgImage(COPY_SVG_ACTIVE);
const DEL_IMG = createSvgImage(DEL_SVG);
const DEL_IMG_ACTIVE = createSvgImage(DEL_SVG_ACTIVE);
let _clipboard;
export const FABRITOR_CUSTOM_PROPS = [
  "id",
  "fabritor_desc",
  "selectable",
  "hasControls",
  "sub_type",
  "imageSource",
  "imageBorder",
  "oldArrowInfo",
];

export const removeObject = (target, canvas) => {
  if (!target) {
    target = canvas.getActiveObject();
  }
  if (!target) return;
  if (target.type === "activeSelection") {
    target.getObjects().forEach((obj) => {
      canvas.remove(obj);
    });
    canvas.discardActiveObject();
  } else {
    canvas.remove(target);
  }
  handleMouseOutCorner(target);
  canvas.requestRenderAll();
  canvas.fire("fabritor:del", { target: null });
  return true;
};

const getType = (type) => {
  if (type.indexOf("text") === 0) {
    return "text";
  }
  if (type.indexOf("image/") === 0) {
    return "image";
  }
  return "";
};

const handleFLinePointsWhenMoving = (opt) => {
  const { target, transform, action } = opt;
  if (action === "line-points-change") return;
  const { original } = transform;
  const deltaLeft = target.left - original.left;
  const deltaTop = target.top - original.top;
  target.set({
    x1: target.x1 + deltaLeft,
    y1: target.y1 + deltaTop,
    x2: target.x2 + deltaLeft,
    y2: target.y2 + deltaTop,
  });
};

const renderSizeIcon = (
  ctx,
  left,
  top,
  styleOverride,
  fabricObject,
  TBorLR
) => {
  const xSize = TBorLR === "TB" ? 20 : 6;
  const ySize = TBorLR === "TB" ? 6 : 20;
  ctx.save();
  ctx.fillStyle = "#ffffff";
  ctx.strokeStyle = "#bbbbbb";
  ctx.lineWidth = 2;
  ctx.shadowBlur = 2;
  ctx.shadowColor = "#dddddd";
  ctx.translate(left, top);
  ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
  ctx.beginPath();
  ctx.roundRect(-xSize / 2, -ySize / 2, xSize, ySize, 10);
  ctx.stroke();
  ctx.fill();
  ctx.restore();
};

const renderLRIcon = (ctx, left, top, styleOverride, fabricObject) => {
  renderSizeIcon(ctx, left, top, styleOverride, fabricObject, "LR");
};

const renderTBIcon = (ctx, left, top, styleOverride, fabricObject) => {
  renderSizeIcon(ctx, left, top, styleOverride, fabricObject, "TB");
};

export const renderVertexIcon = (
  ctx,
  left,
  top,
  styleOverride,
  fabricObject
) => {
  const size = 14;
  ctx.save();
  ctx.fillStyle = "#ffffff";
  ctx.strokeStyle = "#bbbbbb";
  ctx.lineWidth = 2;
  ctx.shadowBlur = 2;
  ctx.shadowColor = "#dddddd";
  ctx.beginPath();
  ctx.arc(left, top, size / 2, 0, 2 * Math.PI, false);
  ctx.stroke();
  ctx.fill();
  ctx.restore();
};

// Simplified render functions
const renderSvgIcon = (icon: HTMLImageElement) => {
  return (
    ctx: CanvasRenderingContext2D,
    left: number,
    top: number,
    styleOverride: any,
    fabricObject: fabric.Object
  ) => {
    const size = 45;
    ctx.save();
    ctx.translate(left, top);
    ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));

    // Combined shadow and background
    ctx.shadowColor = "rgba(0, 0, 0, 0.2)";
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 2;

    ctx.fillStyle = "rgba(255, 255, 255, 0.9)";
    ctx.strokeStyle = "rgba(0, 0, 0, 0.08)";
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(0, 0, size / 2, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Draw icon
    const iconSize = size * 0.6;
    ctx.shadowColor = "transparent";
    ctx.drawImage(icon, -iconSize / 2, -iconSize / 2, iconSize, iconSize);
    ctx.restore();
  };
};

function renderSvgIconActive(icon) {
  return function renderIcon(ctx, left, top, styleOverride, fabricObject) {
    const size = 45;
    ctx.save();
    ctx.translate(left, top);
    ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));

    // Add ripple effect background
    const rippleSize = size * 1.2;
    ctx.fillStyle = "rgba(0, 0, 0, 0.04)";
    ctx.beginPath();
    ctx.arc(0, 0, rippleSize / 2, 0, 2 * Math.PI);
    ctx.fill();

    // Draw button background with subtle gradient
    ctx.fillStyle = "rgba(255, 255, 255, 0.8)";

    // Add subtle elevation
    ctx.shadowColor = "rgba(0, 0, 0, 0.2)";
    ctx.shadowBlur = 6;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 3;

    ctx.beginPath();
    ctx.arc(0, 0, size / 2, 0, 2 * Math.PI);
    ctx.fill();

    // Draw icon with hover effect
    ctx.shadowColor = "transparent";
    const iconSize = size * 0.75;
    ctx.globalAlpha = 0.7; // Slightly transparent on hover
    ctx.drawImage(icon, -iconSize / 2, -iconSize / 2, iconSize, iconSize);

    ctx.restore();
  };
}

const handleCopyObject = async (eventData, transform) => {
  const target = transform.target;
  const canvas = target.canvas;
  const editorElement = canvas.store.getActiveElement();
  if (editorElement) {
    canvas.store.cloneElement(editorElement.id);
  }
  return true;
};

const handleDelObject = (eventData, transform) => {
  const target = transform.target;
  const canvas = target.canvas;
  const editorElement = canvas.store.getActiveElement();
  if (editorElement) {
    canvas.store.deleteElement(editorElement.id);
  }
  return true;
};

// Consolidated control configuration application
const applyControlConfig = (controlName: string, config: ControlConfig) => {
  Object.keys(config).forEach((key) => {
    fabric.Object.prototype.controls[controlName][key] = config[key];
  });
};

export const renderController = () => {
  const controlConfigs: Record<string, ControlConfig> = {
    mt: { x: 0, y: -0.5, offsetY: -1, render: renderTBIcon },
    mb: { x: 0, y: 0.5, offsetY: 1, render: renderTBIcon },
    ml: { x: -0.5, y: 0, offsetX: -1, render: renderLRIcon },
    mr: { x: 0.5, y: 0, offsetX: 1, render: renderLRIcon },
    tl: { x: -0.5, y: -0.5, render: renderVertexIcon },
    tr: { x: 0.5, y: -0.5, render: renderVertexIcon },
    bl: { x: -0.5, y: 0.5, render: renderVertexIcon },
    br: { x: 0.5, y: 0.5, render: renderVertexIcon },
  };

  Object.entries(controlConfigs).forEach(([name, config]) => {
    applyControlConfig(name, config);
  });
};

// reference: https://medium.com/@luizzappa/custom-icon-and-cursor-in-fabric-js-controls-4714ba0ac28f
export const renderRotateController = () => {
  const mtrConfig = {
    x: 0,
    y: 0.5,
    offsetY: 38,
    // TODO change cursor rotation
    cursorStyleHandler: () =>
      `url("data:image/svg+xml;charset=utf-8,${ROTATE_CURSOR}") 12 12, crosshair`,
    render: renderSvgIcon(ROTATE_IMG),
    withConnection: false,
  };
  Object.keys(mtrConfig).forEach((key) => {
    fabric.Object.prototype.controls.mtr[key] = mtrConfig[key];
  });
};

// copy & paste & delete & more
export const renderToolBarController = () => {
  const copyControl = new fabric.Control({
    x: 0,
    y: -0.5,
    offsetX: -25, // 调整间距
    offsetY: -32, // 调高位置
    cursorStyle: "pointer",
    mouseUpHandler: handleCopyObject as any,
    render: renderSvgIcon(COPY_IMG),
  });
  fabric.Object.prototype.controls.copy = copyControl;

  const delControl = new fabric.Control({
    x: 0,
    y: -0.5,
    offsetX: 25, // 调整间距
    offsetY: -32, // 调高位置
    cursorStyle: "pointer",
    mouseUpHandler: handleDelObject,
    render: renderSvgIcon(DEL_IMG),
  });
  fabric.Object.prototype.controls.del = delControl;
};

export const handleMouseOverCorner = (corner, target) => {
  if (corner === "mtr") {
    target.controls[corner].render = renderSvgIconActive(ROTATE_IMG_ACTIVE);
  }
  if (corner === "copy") {
    target.controls[corner].render = renderSvgIconActive(COPY_IMG_ACTIVE);
  }
  if (corner === "del") {
    target.controls[corner].render = renderSvgIconActive(DEL_IMG_ACTIVE);
  }
  target.canvas.requestRenderAll();
};

export const handleMouseOutCorner = (target) => {
  if (!target) return;
  if (target.controls?.mtr) {
    target.controls.mtr.render = renderSvgIcon(ROTATE_IMG);
  }
  if (target.controls?.copy) {
    target.controls.copy.render = renderSvgIcon(COPY_IMG);
  }
  if (target.controls?.del) {
    target.controls.del.render = renderSvgIcon(DEL_IMG);
  }
};

export default function initControl() {
  renderController();
  renderRotateController();
  renderToolBarController();
  initRectControl();
  initLineControl();
  initFTextControl();
}
