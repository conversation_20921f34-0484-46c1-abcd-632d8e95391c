"use client";
import { <PERSON>, But<PERSON>, List, ListItem, Typography } from "@mui/material";
import { observer } from "mobx-react";
import React from "react";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";

const getTextResources = (t: (key: string) => string) => [
  {
    name: t("text_title"),
    fontSize: 35,
    fontWeight: 800,
  },
  {
    name: t("text_subtitle"),
    fontSize: 20,
    fontWeight: 600,
  },
  {
    name: t("text_body"),
    fontSize: 14,
    fontWeight: 400,
  },
  {
    name: t("text_caption"),
    fontSize: 12,
    fontWeight: 400,
  },
];

export const Texts = observer(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const TEXT_RESOURCES = getTextResources(t);

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 1,
        boxShadow: 1,
      }}
    >
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1">{t("Text")}</Typography>
      </Box>
      <Box
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          px: 1,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
        }}
      >
        <List sx={{ py: 0 }}>
          {TEXT_RESOURCES.map((resource) => (
            <ListItem key={resource.name}>
              <Button
                onClick={() => {
                  store.addText({
                    text: resource.name,
                    fontSize: resource.fontSize,
                    fontWeight: resource.fontWeight,
                  });
                  store.saveChange();
                }}
                variant="contained"
                size="small"
                fullWidth
                sx={{
                  textTransform: "none",
                  bgcolor: "action.selected",
                  "&:hover": {
                    bgcolor: "action.selectedHover",
                  },
                }}
              >
                <Typography
                  sx={{
                    flexGrow: 1,
                    color: "text.primary",
                    px: 1,
                    py: 0.5,
                    fontSize: `${resource.fontSize}px`,
                    fontWeight: resource.fontWeight,
                  }}
                >
                  {resource.name}
                </Typography>
              </Button>
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
});
