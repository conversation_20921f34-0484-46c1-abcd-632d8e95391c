import anime from "animejs";
import { fabric } from "fabric";
import { makeAutoObservable } from "mobx";
import {
  Animation,
  EditorElement,
  Effect,
  MenuOption,
  TextEditorElement,
  ImageEditorElement,
  VideoEditorElement,
  TimeFrame,
  BorderStyle,
  Caption,
  TrackType,
  Track,
  ShapeType,
} from "../types";
import {
  getUid,
  isEditorImageElement,
  isEditorVideoElement,
  isHtmlAudioElement,
  isHtmlImageElement,
  isHtmlVideoElement,
} from "../utils";
import { AnimationManager } from "./AnimationManager";
import { ElementManager } from "./ElementManager";
import { VideoSaver } from "./VideoSaver";
import { CaptionManager } from "./CaptionManager";
import { HistoryManager, HistoryActionType } from "./HistoryManager";
import { TrackManager } from "./TrackManager";
import { TemplateManager } from "./TemplateManager";

export class Store {
  canvas: fabric.Canvas | null;
  sketch: any; // 添加类型声明
  canvasWidth: number;
  canvasHeight: number;
  backgroundColor: string;
  projectName: string = "Untitled video";

  // 加载状态相关属性
  isLoading: boolean = false;
  loadingMessage: string = "";

  // 编辑模式：'move'表示可以移动和编辑元素，'hand'表示可以移动和缩放画布
  editMode: "move" | "hand" = "move";

  // 画布缩放相关属性
  canvasScale: number = 0.2; // 默认缩放值
  canvasTranslation: { x: number; y: number } = { x: 0, y: 0 };

  selectedMenuOption: MenuOption;
  audios: string[];
  videos: string[];
  images: string[];
  editorElements: EditorElement[];
  selectedElement: EditorElement | null;

  maxTime: number;
  timelineDisplayDuration: number;
  maxDuration: number;
  animations: Animation[];
  captions: Caption[];
  animationTimeLine: anime.AnimeTimelineInstance;
  playing: boolean;

  currentKeyFrame: number;
  fps: number;

  possibleVideoFormats: string[] = ["mp4", "webm"];
  selectedVideoFormat: string;

  animationFrameId: number | undefined;
  animationManager: AnimationManager | null = null;
  elementManager: ElementManager;
  captionManager: CaptionManager;
  historyManager: HistoryManager;
  trackManager: TrackManager;
  templateManager: TemplateManager;
  _pan: any;
  timelinePan: {
    enabled: boolean;
    isDragging: boolean;
    startX: number;
    offsetX: number;
  };
  cropObject: fabric.Object | null = null;
  cropRect: fabric.Rect | null = null;

  private autoSaveKey = "fabric-canvas-state";
  private debouncedSaveChange: Function;
  private readonly SAVE_DELAY = 500; // 1 second delay

  // 用于标记是否正在拖拽中的状态
  private _isDraggingTimeFrame: boolean = false;

  // 用于标记是否正在导入状态，防止在导入过程中保存历史记录
  private _isImporting: boolean = false;

  constructor() {
    this.canvas = null;
    this.canvasWidth = 1280;
    this.canvasHeight = 720;
    this._pan = {
      enable: false,
      isDragging: false,
      lastPosX: 0,
      lastPosY: 0,
    };
    this.timelinePan = {
      enabled: true,
      isDragging: false,
      startX: 0,
      offsetX: 0,
    };
    this.videos = [];
    this.images = [];
    this.audios = [];
    this.editorElements = [];
    this.backgroundColor = "#111111";
    //时间线视图的maxTime
    this.maxTime = 60 * 60 * 1000;
    this.timelineDisplayDuration = this.maxTime;
    this.maxDuration = 30 * 1000;
    this.playing = false;
    this.currentKeyFrame = 0;
    this.selectedElement = null;
    this.fps = 60;
    this.animations = [];
    this.animationTimeLine = anime.timeline();
    this.selectedMenuOption = "Video";
    this.selectedVideoFormat = "mp4";
    this.initializeManagers();

    makeAutoObservable(this, {
      canvas: false,
      animationTimeLine: false,
      animationManager: false,
      elementManager: false,
      captionManager: false,
      historyManager: false,
      trackManager: false,
      templateManager: false,
    });
    this.debouncedSaveChange = this.debounce(
      (actionType?: HistoryActionType) => {
        this.saveToLocalStorage();
        this.saveToHistory(actionType);
      },
      this.SAVE_DELAY
    );

    // Set a reasonable initial timeline display duration (10 seconds)
    this.setTimelineDisplayDuration(10000);

    // 更新最大时间
    this.updateMaxTime();

    // Auto fit timeline if elements exist
    if (this.editorElements.length > 0) {
      this.fitTimelineToContent();
    }

    // 初始化历史记录
    this.historyManager.initHistory();
  }

  private initializeManagers() {
    this.animationManager = new AnimationManager(this);
    this.elementManager = new ElementManager(this);
    this.captionManager = new CaptionManager(this);
    this.historyManager = new HistoryManager(this);
    this.trackManager = new TrackManager(this);
    this.templateManager = new TemplateManager(this);
    this.captions = this.captionManager.captions;

    // 初始化轨道
    this.trackManager.initializeTracks();

    // 在开发环境中将store添加到全局，方便调试
    if (process.env.NODE_ENV === "development") {
      (window as any).store = this;
      console.log(
        "🔧 开发模式：store已添加到全局，可使用 window.debugTracks() 和 window.cleanupTracks() 进行调试"
      );
    }
  }

  setCanvasSize(width: number, height: number) {
    this.canvasWidth = width;
    this.canvasHeight = height;
    if (this.canvas) {
      this.canvas.setWidth(width);
      this.canvas.setHeight(height);
      this.canvas.renderAll();
    }
    this.refreshElements();
    this.saveChange("画布调整");
  }

  get currentTimeInMs() {
    return (this.currentKeyFrame * 1000) / this.fps;
  }

  setCurrentTimeInMs(time: number) {
    const oldKeyFrame = this.currentKeyFrame;
    this.currentKeyFrame = Math.floor((time / 1000) * this.fps);
  }

  setSelectedMenuOption(selectedMenuOption: MenuOption) {
    this.selectedMenuOption = selectedMenuOption;
  }

  setCanvas(canvas: any) {
    this.canvas = canvas;
    if (canvas) {
      canvas.backgroundColor = this.backgroundColor;
      this.elementManager.setCanvas(canvas);
    }
  }

  setBackgroundColor(backgroundColor: string) {
    this.backgroundColor = backgroundColor;
    if (this.canvas) {
      if (backgroundColor.startsWith("linear-gradient")) {
        const gradientObj = new fabric.Gradient({
          type: "linear",
          coords: { x1: 0, y1: 0, x2: this.canvas.width, y2: 0 },
          colorStops: [
            { offset: 0, color: backgroundColor.match(/#[a-f\d]{6}/gi)[0] },
            { offset: 1, color: backgroundColor.match(/#[a-f\d]{6}/gi)[1] },
          ],
        });

        this.canvas.setBackgroundColor(
          gradientObj,
          this.canvas.renderAll.bind(this.canvas)
        );
      } else {
        this.canvas.backgroundColor = backgroundColor;
        this.canvas.renderAll();
      }
    }
    this.saveChange("画布调整");
  }

  updateEffect(id: string, effect: Effect) {
    console.log(effect);
    const index = this.editorElements.findIndex((element) => element.id === id);
    const element = this.editorElements[index];
    if (isEditorVideoElement(element) || isEditorImageElement(element)) {
      element.properties.effect = effect;
    }
    this.refreshElements();
  }

  setVideos(videos: string[]) {
    this.videos = videos;
  }

  addVideoResource(video: string) {
    this.videos = [...this.videos, video];
  }
  addAudioResource(audio: string) {
    this.audios = [...this.audios, audio];
  }
  addImageResource(image: string) {
    this.images = [...this.images, image];
  }

  addAnimation(animation: any) {
    this.animations = [...this.animations, animation];
    this.animationManager.refreshAnimations();
  }
  updateAnimation(id: string, animation: Animation) {
    const index = this.animations.findIndex((a) => a.id === id);
    if (index !== -1) {
      this.animations[index] = animation;
      this.animationManager?.refreshAnimations();
    } else {
      console.warn(`Animation with id ${id} not found`);
    }
  }

  getAnimation(id: string): Animation | undefined {
    return this.animations.find((animation) => animation.id === id);
  }
  /**
   * 刷新动画。
   * 该方法会清除当前的动画时间线，并创建一个新的动画时间线。
   * 遍历所有的动画对象，根据动画类型添加相应的动画效果到时间线中。
   * 最后，启动动画时间线。
   */
  refreshAnimations() {
    this.animationManager.refreshAnimations();
  }

  /**
   * 从动画列表中移除指定的动画。
   * @param id 要移除的动画的唯一标识符。
   * @returns 无返回值。
   */
  removeAnimation(id: string) {
    if (!id) return;
    const animation = this.animations.find((animation) => animation.id === id);
    const elementId = animation?.targetId;
    const element = this.editorElements.find(
      (element) => element.id === elementId
    );
    if (animation) {
      this.animations = this.animations.filter(
        (animation) => animation.id !== id
      );
      this.animationManager.refreshAnimations();
    }
    this.updateEditorElement(element);
    this.canvas.renderAll();
  }

  /**
   * 设置元素。
   * @param selectedElement 要设置为选中的元素，可以是 EditorElement 对象或 null。
   * @returns 无返回值。
   */
  setSelectedElement(selectedElement: EditorElement | null) {
    // 如果选中了时间线元素，取消所有字幕的选中状态
    if (selectedElement) {
      this.captionManager.deselectAllCaptions();
    }

    this.selectedElement = selectedElement;
    if (this.canvas) {
      if (selectedElement?.fabricObject) {
        this.canvas.setActiveObject(selectedElement.fabricObject);
      } else {
        this.canvas.discardActiveObject();
      }
      // 强制重新渲染画布以确保选中状态正确显示
      this.canvas.requestRenderAll();
    }
  }

  updateSelectedElement() {
    if (!this.selectedElement) {
      return;
    }
    const updatedElement = this.editorElements.find(
      (element) => element.id === this.selectedElement!.id
    );
    this.selectedElement = updatedElement || null;
  }

  /**
   * 设置编辑元素数组。
   * @param editorElements 要设置的编辑元素数组。
   * @returns 无返回值。
   */
  setEditorElements(editorElements: EditorElement[]) {
    this.editorElements = editorElements;
    this.updateSelectedElement();
    // this.refreshElements();
    // this.refreshAnimations();

    // Only auto-fit when elements actually exist
    if (editorElements.length > 0) {
      // this.fitTimelineToContent();
    }
  }
  /**
   * 更新编辑元素。
   * @param editorElement 要更新的编辑元素。
   * @param actionType 操作类型，默认为"元素修改"
   * @returns 无返回值。
   */
  updateEditorElement(
    editorElement: EditorElement,
    actionType: HistoryActionType = "元素修改"
  ) {
    if (!editorElement) return;
    console.log(editorElement);
    this.setEditorElements(
      this.editorElements.map((element) =>
        element.id === editorElement.id ? editorElement : element
      )
    );
    //this.refreshElement(editorElement);
    this.saveChange(actionType);
  }

  // 获取拖拽状态
  get isDraggingTimeFrame(): boolean {
    return this._isDraggingTimeFrame;
  }

  // 设置拖拽状态 - 开始拖拽
  setDraggingTimeFrameStart() {
    this._isDraggingTimeFrame = true;
  }

  // 设置拖拽状态 - 结束拖拽
  setDraggingTimeFrameEnd() {
    this._isDraggingTimeFrame = false;
    // 在拖拽结束时触发一次完整的元素刷新
    this.refreshElements();
  }

  /**
   * 更新编辑器元素的时间帧
   * 优化版本：在拖拽过程中只更新状态，不触发重渲染
   * @param editorElement 要更新的元素
   * @param timeFrame 新的时间帧
   * @param isDragEnd 是否是拖拽结束的更新
   */
  updateEditorElementTimeFrame(
    editorElement: EditorElement,
    timeFrame: Partial<TimeFrame>,
    isDragEnd: boolean = false
  ) {
    const index = this.editorElements.findIndex(
      (element) => element.id === editorElement.id
    );
    if (index === -1) return;

    const element = this.editorElements[index];
    element.timeFrame = {
      ...element.timeFrame,
      ...timeFrame,
    };

    // 只在拖拽结束或非拖拽状态下执行重渲染
    if (isDragEnd || !this._isDraggingTimeFrame) {
      this.refreshElements();
      this.updateMediaElements();

      // 如果是拖拽结束，保存更改并更新最大时间
      if (isDragEnd) {
        // 更新最大时间，确保播放和indicator不超过最大endtime
        this.updateMaxTime();

        // 开始分组操作，将连续的时间帧调整视为一个操作
        if (!this.historyManager.isGrouping) {
          this.startHistoryGroup("元素移动");
        }
        this.saveChange("元素移动");

        // 延迟结束分组，允许短时间内的连续操作被视为一组
        setTimeout(() => {
          this.endHistoryGroup();
        }, 500);
      }
    } else {
      // 在拖拽过程中，只更新媒体元素的当前时间，不触发完整的重渲染
      // 这可以大大提高拖拽性能
      this.updateMediaElementsLite(editorElement.id);
    }
  }

  /**
   * 轻量版的媒体元素更新，只更新当前拖拽的元素
   * 用于拖拽过程中提高性能
   * @param currentElementId 当前正在拖拽的元素ID
   */
  updateMediaElementsLite(currentElementId: string) {
    // 只处理当前拖拽的元素，如果是视频或音频
    const element = this.editorElements.find(
      (el) => el.id === currentElementId
    );
    if (!element || (element.type !== "video" && element.type !== "audio"))
      return;

    const media = document.getElementById(element.properties.elementId);
    if (isHtmlVideoElement(media) || isHtmlAudioElement(media)) {
      // 检查是否有自定义的媒体开始时间（用于分割后的元素）
      const mediaStartTime = (element.properties as any).mediaStartTime || 0;

      // 计算媒体当前时间：当前时间 - 元素开始时间（转换为秒）+ 媒体开始时间偏移
      const mediaTime =
        (this.currentTimeInMs - element.timeFrame.start) / 1000 +
        mediaStartTime;

      media.currentTime = Math.max(0, mediaTime);
    }
  }

  /**
   * 添加编辑器元素
   * @param editorElement 要添加的元素
   */
  addEditorElement(editorElement: EditorElement) {
    this.setEditorElements([editorElement, ...this.editorElements]);
    this.elementManager.addElement(editorElement);
    this.refreshAnimations();
    this.updateTimeTo(this.currentTimeInMs);

    // 将新元素添加到适当的轨道，并获取碰撞检测结果
    const collisionResult = this.trackManager.handleNewElement(editorElement);

    // 如果发生了碰撞并调整了时间，只记录日志
    if (collisionResult && collisionResult.hasOverlap) {
      console.log(
        `智能碰撞检测：元素 "${editorElement.name}" 的时间已从 ${collisionResult.originalTime.start}ms-${collisionResult.originalTime.end}ms 调整为 ${collisionResult.adjustedTime.start}ms-${collisionResult.adjustedTime.end}ms 以避免与轨道中的其他元素重叠`
      );
    }

    // 更新最大时间
    this.updateMaxTime();

    // 根据轨道顺序更新Canvas上的元素显示顺序
    this.updateCanvasOrderByTrackOrder();

    if (!this.canvas) return;
    const element = this.editorElements.find(
      (el) => el.id === editorElement.id
    );
    if (!element) return;
    this.setSelectedElement(element);
    this.canvas.renderAll();
    this.saveToHistory("元素添加");

    // Auto fit timeline to show all elements
    //this.fitTimelineToContent();
  }

  /**
   * 移除编辑器元素
   * @param id 要移除的元素ID
   */
  removeEditorElement(id: string) {
    console.log(`Removing editor element: ${id}`);

    // 获取要删除的元素，用于日志记录
    const elementToRemove = this.editorElements.find((el) => el.id === id);
    if (elementToRemove) {
      console.log(
        `Removing element: ${elementToRemove.name}, type: ${elementToRemove.type}`
      );
    }

    // Remove any animations associated with this element
    this.animations = this.animations.filter(
      (animation) => animation.targetId !== id
    );

    // 从轨道中移除元素
    this.trackManager.handleElementDeleted(id);

    // 从canvas中移除元素
    const fabricObject = elementToRemove?.fabricObject;
    if (fabricObject && this.canvas) {
      this.canvas.remove(fabricObject);
    }

    // Remove the element from editorElements array
    this.setEditorElements(
      this.editorElements.filter((editorElement) => editorElement.id !== id)
    );

    // 移除DOM中的元素（如果存在）
    // 首先检查是否是媒体元素，如果是，需要移除对应的媒体元素
    if (
      elementToRemove &&
      (elementToRemove.type === "video" ||
        elementToRemove.type === "audio" ||
        elementToRemove.type === "image")
    ) {
      const mediaElementId = elementToRemove.properties?.elementId;
      if (mediaElementId) {
        const mediaElement = document.getElementById(mediaElementId);
        if (mediaElement) {
          console.log(`Removing media element from DOM: ${mediaElementId}`);
          mediaElement.remove();
        }
      }
    }

    // 移除DOM中的元素本身（如果存在）
    const element = document.getElementById(id);
    console.log("remove document", id);
    if (element) {
      element.remove();
    }

    // 移除当前选中的对象（如果是被删除的元素）
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      this.canvas.remove(activeObject);
      this.canvas.requestRenderAll();
    }

    // 刷新元素和动画
    this.refreshElements();
    this.refreshAnimations(); // Refresh animations after removing them

    // 保存历史记录
    this.saveToHistory("元素删除");

    // 保存当前时间，用于后续检查
    const currentTime = this.currentTimeInMs;

    // 更新最大时间
    this.updateMaxTime();

    // 如果当前时间超过了新的最大endtime，则调整indicator位置到最大endtime
    if (this.maxDuration > 0 && currentTime > this.maxDuration) {
      this.handleSeek(this.maxDuration);
    }

    // Auto fit timeline to show all elements after removal
    //this.fitTimelineToContent();
  }

  setMaxTime(maxTime: number) {
    this.maxTime = maxTime;
  }

  updateMaxTime() {
    // 保存当前时间，用于后续检查
    const currentTime = this.currentTimeInMs;

    // 获取所有元素的最大endtime
    const maxElementEndTime = this.editorElements.reduce((max, element) => {
      return Math.max(max, element.timeFrame.end);
    }, 0);

    // 获取所有字幕的最大endtime（转换为毫秒）
    const maxCaptionEndTime = this.captionManager.captions.reduce(
      (max, caption) => {
        const captionEndTimeMs = this.captionManager.timeStringToMilliseconds(
          caption.endTime
        );
        return Math.max(max, captionEndTimeMs);
      },
      0
    );

    // 取元素和字幕中的最大endtime
    const maxEndTime = Math.max(maxElementEndTime, maxCaptionEndTime);

    // Set a minimum max time of 30 seconds if no elements or captions exist
    this.maxDuration = maxEndTime > 0 ? maxEndTime : 30 * 1000;

    // 如果当前时间超过了新的最大endtime，则调整indicator位置到最大endtime
    if (this.maxDuration > 0 && currentTime > this.maxDuration) {
      this.handleSeek(this.maxDuration);
    }
  }

  setPlaying(playing: boolean) {
    // 如果没有元素，则不允许开始播放
    if (playing && this.editorElements.length === 0) {
      console.log("没有元素，无法开始播放");
      return;
    }

    this.playing = playing;

    // 在开始播放前更新所有媒体元素的当前时间
    this.updateMediaElements();

    if (playing) {
      this.startedTime = Date.now();
      this.startedTimePlay = this.currentTimeInMs;
      this.animationFrameId = requestAnimationFrame(() => {
        this.playFrames();
      });
    } else {
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = undefined;
      }
    }
  }

  startedTime = 0;
  startedTimePlay = 0;

  playFrames = () => {
    if (!this.playing) return;

    // 使用 performance.now() 代替 Date.now() 以获得更高精度的时间戳
    const newTime = Date.now() - this.startedTime + this.startedTimePlay;

    // 如果超过最大时间或超过所有元素的最大endtime，停止播放
    if (
      newTime >= this.maxTime ||
      (this.maxDuration > 0 && newTime >= this.maxDuration)
    ) {
      this.currentKeyFrame = 0;
      this.setPlaying(false);
      // 如果超过最大时间，重置到开始；如果超过最大endtime，停留在最大endtime
      const targetTime = newTime >= this.maxTime ? 0 : this.maxDuration;
      this.updateTimeTo(targetTime);
      return;
    }

    // 使用 requestAnimationFrame 而不是 fabric.util.requestAnimFrame
    // 因为原生 API 性能更好
    this.animationFrameId = requestAnimationFrame(this.playFrames);

    // 批量更新以减少重绘
    this.updateTimeTo(newTime);
  };

  /**
   * 更新时间线到指定时间点
   *
   * 该方法负责：
   * 1. 设置当前时间
   * 2. 更新时间线位置
   * 3. 处理元素的可见性
   * 4. 控制媒体元素的播放状态
   * 5. 更新字幕显示
   *
   * @param newTime 新的时间点（毫秒）
   */
  updateTimeTo(newTime: number) {
    // 更新当前时间
    this.setCurrentTimeInMs(newTime);

    // 更新动画时间线位置
    this.animationTimeLine.seek(newTime);

    // 优化：使用一次循环处理所有元素的可见性和媒体播放
    for (const element of this.editorElements) {
      // 检查元素是否在当前时间点可见
      const isVisible =
        element.timeFrame.start <= newTime && newTime <= element.timeFrame.end;

      // 更新元素可见性
      if (element.fabricObject) {
        element.fabricObject.visible = isVisible;
      }

      // 处理媒体元素（视频和音频）
      if (element.type === "video" || element.type === "audio") {
        const media = document.getElementById(
          element.properties.elementId
        ) as HTMLMediaElement;

        if (media) {
          // 获取媒体元素的自定义属性
          const mediaStartTime =
            (element.properties as any).mediaStartTime || 0;
          const originalPlaybackSpeed = (element as any).playbackSpeed || 1;

          // 根据元素可见性和播放状态控制媒体播放
          if (isVisible && this.playing && media.paused) {
            // 计算正确的媒体时间点，考虑播放速度和偏移
            const mediaTime =
              ((newTime - element.timeFrame.start) / 1000) *
                originalPlaybackSpeed +
              mediaStartTime;

            // 设置播放速度和当前时间
            media.playbackRate = originalPlaybackSpeed;
            media.currentTime = Math.max(0, mediaTime);

            // 开始播放媒体
            media
              .play()
              .catch((err) =>
                console.error(`Error playing ${element.type}:`, err)
              );
          } else if (!isVisible || (!this.playing && !media.paused)) {
            // 如果元素不可见或者不在播放状态，暂停媒体
            media.pause();
          }
        }
      }
    }

    // 更新字幕显示
    this.captionManager.updateCurrentCaption(newTime);

    // 请求重新渲染画布
    this.canvas.requestRenderAll();
  }

  /**
   * 处理时间线跳转
   * 该方法负责在用户在时间线上点击或拖动指示器时更新视图
   *
   * @param seek 跳转的目标时间（毫秒）
   */
  handleSeek(seek: number) {
    console.log(`handleSeek: 跳转到 ${seek}ms`);

    // 如果正在播放，先停止播放
    if (this.playing) {
      this.setPlaying(false);
    }

    // 更新时间线位置和元素可见性
    this.updateTimeTo(seek);

    // 只在必要时刷新动画（例如，当有动画元素时）
    if (this.animations.length > 0) {
      this.refreshAnimations();
    }

    // 确保媒体元素状态与当前时间同步
    // 这个调用是必要的，因为它处理了一些updateTimeTo中未处理的媒体状态
    this.updateMediaElements();

    console.log(`handleSeek完成: 当前时间为 ${this.currentTimeInMs}ms`);
  }

  async checkAudioStream(videoElement: HTMLVideoElement) {
    const stream = videoElement.srcObject as MediaStream;
    if (stream && stream.getAudioTracks && stream.getAudioTracks().length > 0) {
      return true;
    } else {
      return false;
    }
  }

  async addVideoElement(
    videoElement: HTMLVideoElement,
    videoElementId: string
  ) {
    const videoDurationMs = Number((videoElement.duration * 1000).toFixed(2));
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();
    const aspectRatio = videoElement.videoWidth / videoElement.videoHeight;
    const id = videoElementId;
    const videoHeight = 400;
    const videoWidth = videoHeight * aspectRatio;

    this.addEditorElement({
      id,
      locked: false,
      opacity: 1,
      name: `Media(video) ${this.videos.length}`,
      type: "video",
      placement: {
        x: Number(((canvasWidth - videoWidth) / 2).toFixed(2)),
        y: Number(((canvasHeight - videoHeight) / 2).toFixed(2)),
        width: Number(videoWidth.toFixed(2)),
        height: Number(videoHeight.toFixed(2)),
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
        flipX: false,
        flipY: false,
      },
      timeFrame: {
        start: 0,
        end: videoDurationMs,
      },
      properties: {
        elementId: `video-${id}`,
        src: videoElement.src,
        originalDuration: videoDurationMs, // 保存视频的原始时长
        effect: {
          type: "none",
        },
        filters: {
          type: "none",
        },
        border: {
          width: 0,
          color: "black",
          style: "solid",
          borderRadius: 0,
        },
      },
    });
    this.updateMediaElements();
    this.saveChange();
  }

  addVideo(index: string) {
    const videoElement = document.getElementById(`video-${index}`);
    if (!isHtmlVideoElement(videoElement)) {
      return;
    }
    this.addVideoElement(videoElement, index);
  }

  addImageElement(imageElement: HTMLImageElement, elementId: string) {
    const aspectRatio = imageElement.naturalWidth / imageElement.naturalHeight;
    const id = elementId;
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();
    const imageWidth = 300 * aspectRatio;
    const imageHeight = 300;
    this.addEditorElement({
      id,
      opacity: 1,
      locked: false,
      name: `Media(image) ${this.images.length}`,
      type: "image",
      placement: {
        x: Number(((canvasWidth - imageWidth) / 2).toFixed(2)),
        y: Number(((canvasHeight - imageHeight) / 2).toFixed(2)),
        width: Number(imageWidth.toFixed(2)),
        height: Number(imageHeight.toFixed(2)),
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
        flipX: false,
        flipY: false,
      },
      timeFrame: {
        start: 0,
        end: 3000,
      },
      properties: {
        elementId: `image-${id}`,
        src: imageElement.src,
        effect: {
          type: "none",
        },
        filters: {
          type: "none",
        },
        border: {
          width: 0,
          color: "black",
          style: "solid",
          borderRadius: 0,
        },
      },
    });
    this.saveChange();
  }

  addImage(id: string) {
    const imageElement = document.getElementById(`image-${id}`);
    if (!isHtmlImageElement(imageElement)) {
      return;
    }
    this.addImageElement(imageElement, id);
  }

  addAudioElement(audioElement: HTMLAudioElement, audioElementId: string) {
    const audioDurationMs = audioElement.duration * 1000;
    const id = audioElementId;
    this.addEditorElement({
      id,
      locked: false,
      name: `Media(audio) ${this.audios.length}`,
      type: "audio",
      timeFrame: {
        start: 0,
        end: audioDurationMs,
      },
      properties: {
        elementId: `audio-${id}`,
        src: audioElement.src,
        originalDuration: audioDurationMs, // 保存音频的原始时长
      },
    });
    this.saveChange();
  }

  addAudio(index: string) {
    const audioElement = document.getElementById(`audio-${index}`);
    if (!isHtmlAudioElement(audioElement)) {
      return;
    }
    this.addAudioElement(audioElement, index);
  }

  addText(options: {
    text: string;
    fontSize: number;
    fontWeight: number;
    id?: string;
  }) {
    const { text, fontSize, fontWeight, id: optionsId } = options;

    const elementId = optionsId || getUid();
    const index = this.editorElements.length;

    const editorWidth = this.canvas.getWidth() || 1920;
    const editorHeight = this.canvas.getHeight() || 1080;

    const textConfig = {
      fontSize,
      fontWeight,
      fontFamily: "Arial",
    };

    const tempText = new fabric.Text(text, textConfig);
    const { width: textWidth, height: textHeight } = tempText;

    const placement = {
      x: Math.round(((editorWidth - textWidth) / 2) * 100) / 100,
      y: Math.round(((editorHeight - textHeight) / 2) * 100) / 100,
      width: Math.round(textWidth * 100) / 100,
      height: Math.round(textHeight * 100) / 100,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    };

    this.addEditorElement({
      id: elementId,
      locked: false,
      opacity: 1,
      name: `Text ${index + 1}`,
      type: "text",
      placement,
      timeFrame: {
        start: 0,
        end: 5000,
      },
      properties: {
        text,
        fontSize,
        fontWeight,
        splittedTexts: [],
        fontFamily: "Arial",
        fontColor: "#ffffff", // 添加默认字体颜色
      },
    });
    this.saveChange();
  }

  /**
   * 添加图形元素
   * @param shapeType 图形类型
   * @param options 可选配置
   */
  addShapeElement(
    shapeType: ShapeType,
    options?: {
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      borderRadius?: number;
      id?: string;
    }
  ) {
    const elementId = options?.id || getUid();
    const index = this.editorElements.length;
    const editorWidth = this.canvas.getWidth() || 1920;
    const editorHeight = this.canvas.getHeight() || 1080;

    // 默认尺寸和位置
    let width = 200;
    let height = 200;

    // 根据形状类型调整尺寸
    if (shapeType === "line") {
      width = 300;
      height = 5;
    } else if (shapeType === "ellipse") {
      width = 250;
      height = 150;
    } else if (shapeType === "parallelogram") {
      width = 250;
      height = 150;
    }

    const placement = {
      x: Math.round(((editorWidth - width) / 2) * 100) / 100,
      y: Math.round(((editorHeight - height) / 2) * 100) / 100,
      width: width,
      height: height,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    };

    // 创建图形元素
    this.addEditorElement({
      id: elementId,
      locked: false,
      opacity: 1,
      name: `Shape ${index + 1}`,
      type: "shape",
      placement,
      timeFrame: {
        start: 0,
        end: 5000,
      },
      properties: {
        shapeType: shapeType,
        fill: options?.fill || "#9e9e9e",
        stroke: options?.stroke || "#757575",
        strokeWidth: options?.strokeWidth || 1,
        borderRadius:
          options?.borderRadius || (shapeType === "roundedRect" ? 10 : 0),
        border: {
          width: 0,
          color: "transparent",
          style: "solid",
          borderRadius: 0,
        },
      },
    });
    this.saveChange();
  }

  /**
   * 更新所有媒体元素的状态
   * 该方法确保所有媒体元素（视频和音频）的播放状态和当前时间与时间线同步
   */
  updateMediaElements() {
    // 只处理视频和音频元素
    const mediaElements = this.editorElements.filter(
      (element) => element.type === "video" || element.type === "audio"
    );

    for (const element of mediaElements) {
      const media = document.getElementById(element.properties.elementId);
      if (!isHtmlVideoElement(media) && !isHtmlAudioElement(media)) continue;

      // 获取媒体元素的自定义属性
      const mediaStartTime = (element.properties as any).mediaStartTime || 0;
      const originalPlaybackSpeed = (element as any).playbackSpeed || 1;

      // 计算媒体当前时间，考虑播放速度
      const mediaTime = Math.max(
        0,
        ((this.currentTimeInMs - element.timeFrame.start) / 1000) *
          originalPlaybackSpeed +
          mediaStartTime
      );

      // 设置媒体元素的当前时间和播放速度
      media.currentTime = mediaTime;
      media.playbackRate = originalPlaybackSpeed;

      // 检查元素是否在当前时间范围内
      const isWithinTimeFrame =
        this.currentTimeInMs >= element.timeFrame.start &&
        this.currentTimeInMs <= element.timeFrame.end;

      // 根据元素可见性和播放状态控制媒体播放
      if (isWithinTimeFrame && this.playing && media.paused) {
        // 如果应该播放但当前是暂停状态，开始播放
        media
          .play()
          .catch((e) => console.error(`播放${element.type}时出错:`, e));
      } else if ((!isWithinTimeFrame || !this.playing) && !media.paused) {
        // 如果不应该播放但当前是播放状态，暂停播放
        media.pause();
      }
    }
  }

  setVideoFormat(format: string) {
    this.selectedVideoFormat = format;
  }

  saveCanvasToVideoWithAudio() {
    const canvas = document.getElementById("myCanvas") as HTMLCanvasElement;
    const videoSaver = new VideoSaver(
      canvas,
      this.editorElements,
      this.maxTime,
      this.selectedVideoFormat
    );
    videoSaver.saveCanvasToVideoWithAudio();
  }

  refreshElements() {
    this.elementManager.refreshElements();
  }

  refreshElement(element: EditorElement) {
    this.elementManager.refreshElement(element);
  }

  alignElement(id: string, alignType: string) {
    this.elementManager.alignElement(id, alignType);
    this.saveChange();
  }

  toggleLockElement(id: string) {
    this.elementManager.toggleLockElement(id);
    this.saveChange("元素属性修改");
  }

  cloneElement(id: string) {
    this.elementManager.cloneElement(id);
    this.saveChange(); // Add this line
  }

  /**
   * 在指定时间点分割时间线元素
   * @param id 要分割的元素ID
   * @param splitTime 分割时间点（毫秒）
   */
  splitElement(id: string, splitTime: number) {
    // 查找要分割的元素
    console.log("split element");
    const elementIndex = this.editorElements.findIndex((el) => el.id === id);
    if (elementIndex === -1) return;

    const element = this.editorElements[elementIndex];

    // 检查分割时间是否在元素的时间范围内
    if (
      splitTime <= element.timeFrame.start ||
      splitTime >= element.timeFrame.end
    ) {
      console.warn("Split time must be between element start and end time");
      return;
    }

    // 创建第一个元素（原始元素的前半部分）
    const firstElement = { ...element };
    firstElement.timeFrame = {
      start: element.timeFrame.start,
      end: splitTime,
    };

    // 创建第二个元素（原始元素的后半部分）
    const secondElement = { ...JSON.parse(JSON.stringify(element)) };
    secondElement.id = getUid(); // 生成新的ID
    secondElement.name = `${element.name} (split)`;
    secondElement.timeFrame = {
      start: splitTime,
      end: element.timeFrame.end,
    };

    // 处理特殊类型元素的额外属性
    if (element.type === "video" || element.type === "audio") {
      // 为第二个元素创建新的媒体元素ID
      const originalElementId = element.properties.elementId;
      const newElementId = originalElementId.replace(
        element.id,
        secondElement.id
      );
      secondElement.properties.elementId = newElementId;

      // 确保原始duration被保留
      if (element.properties.originalDuration) {
        secondElement.properties.originalDuration =
          element.properties.originalDuration;
      }

      // 复制媒体元素
      const originalMedia = document.getElementById(
        originalElementId
      ) as HTMLMediaElement;
      if (originalMedia) {
        const newMedia = originalMedia.cloneNode(true) as HTMLMediaElement;
        newMedia.id = newElementId;

        // 计算媒体偏移时间 - 这是关键改进
        // 我们需要在媒体元素的properties中添加一个自定义属性来存储偏移时间
        // 由于TypeScript类型限制，我们使用类型断言来添加这个属性

        // 获取原始元素的媒体开始时间（如果有的话）
        const originalMediaStartTime =
          (element.properties as any).mediaStartTime || 0;

        // 获取播放速度（如果有的话）
        const originalPlaybackSpeed = (element as any).playbackSpeed || 1;

        // 计算新的偏移时间：原始偏移时间 + (分割点 - 原始开始时间) / 1000 * 播放速度
        // 先计算时间线上的时间差，然后乘以播放速度，得到媒体内部的时间差
        const additionalOffset =
          ((splitTime - element.timeFrame.start) / 1000) *
          originalPlaybackSpeed;
        const mediaOffset = originalMediaStartTime + additionalOffset;

        // 使用类型断言添加自定义属性
        (secondElement.properties as any).mediaStartTime = mediaOffset;

        console.log(
          `分割元素 ${element.id}，原始偏移: ${originalMediaStartTime}秒，额外偏移: ${additionalOffset}秒，新偏移: ${mediaOffset}秒，播放速度: ${originalPlaybackSpeed}x`
        );

        // 设置媒体元素的当前时间为新的偏移时间
        newMedia.currentTime = mediaOffset;

        // 保留原始元素的播放速度
        // 使用类型断言来设置playbackSpeed属性
        if (originalPlaybackSpeed !== undefined) {
          (secondElement as any).playbackSpeed = originalPlaybackSpeed;
          newMedia.playbackRate = originalPlaybackSpeed;
          console.log(`保留播放速度: ${originalPlaybackSpeed}x`);
        }

        // 保留原始元素的音量
        // 使用类型断言来访问和设置volume属性
        const originalVolume = (element as any).volume;
        if (originalVolume !== undefined) {
          (secondElement as any).volume = originalVolume;
          newMedia.volume = originalVolume;
          console.log(`保留音量: ${originalVolume}`);
        } else {
          // 如果没有设置音量，默认设置为1
          (secondElement as any).volume = 1;
          newMedia.volume = 1;
        }

        document.body.appendChild(newMedia);
      }
    }

    // 清除第二个元素的fabricObject引用，让它重新创建
    secondElement.fabricObject = undefined;

    // 更新第一个元素（原始元素）
    this.updateEditorElement(firstElement);

    // 添加第二个元素
    this.addEditorElement(secondElement);

    // 刷新元素和保存更改
    this.refreshElements();

    // 更新最大时间
    this.updateMaxTime();

    this.saveChange();
  }

  deleteElement(id: string) {
    // 添加调试日志
    console.log(`Store.deleteElement called for id: ${id}`);

    // 先从轨道中移除元素
    this.trackManager.handleElementDeleted(id);

    // 然后删除元素（ElementManager.deleteElement方法中已经处理了indicator位置调整）
    this.elementManager.deleteElement(id);

    // 保存更改
    this.saveChange();
  }

  /**
   * 清理空轨道（现在默认包括默认轨道）
   * 用于调试和手动清理
   */
  cleanupEmptyTracks() {
    console.log("🧹 清理所有空轨道（包括默认轨道）...");
    const hasRemovedTracks = this.trackManager.removeEmptyTracks();
    if (hasRemovedTracks) {
      console.log("✅ 清理完成");
    } else {
      console.log("ℹ️ 没有空轨道需要清理");
    }
    return hasRemovedTracks;
  }

  /**
   * 保护性清理空轨道（保留默认轨道）
   * 用于需要保留默认轨道的场景
   */
  cleanupEmptyTracksPreserveDefaults() {
    console.log("🧹 清理空轨道（保留默认轨道）...");
    const hasRemovedTracks = this.trackManager.removeEmptyTracks(true);
    if (hasRemovedTracks) {
      console.log("✅ 保护性清理完成");
    } else {
      console.log("ℹ️ 没有空轨道需要清理");
    }
    return hasRemovedTracks;
  }

  setElementFullscreen(id: string) {
    this.elementManager.setElementFullscreen(id);
    this.saveChange();
  }

  updateElementOpacity(id: string, opacity: number) {
    this.elementManager.updateElementOpacity(id, opacity);
    this.saveChange();
  }

  startCropMode(id: string) {
    this.elementManager.startCropMode(id);
  }

  applyCrop() {
    this.elementManager.applyCrop();
  }

  cancelCrop() {
    this.elementManager.cancelCrop();
  }

  updateTextStyle(
    elementId: string,
    style: Partial<TextEditorElement["properties"]>
  ) {
    this.elementManager.updateTextStyle(elementId, style);
    this.saveChange();
  }

  reorderElements(
    startIndex: number,
    endIndex: number,
    placement?: "above" | "below"
  ) {
    // 记录操作开始时间，用于性能监测
    const startTime = performance.now();

    // 根据放置位置调整目标索引
    let adjustedEndIndex = endIndex;

    // 判断拖拽方向
    const isDraggingUp = startIndex > endIndex;
    const isDraggingDown = startIndex < endIndex;

    // 更精确地处理放置位置
    if (placement === "below") {
      if (isDraggingUp) {
        // 向上拖拽并放置在下方，目标索引加1，但不超过数组长度
        adjustedEndIndex = Math.min(
          endIndex + 1,
          this.editorElements.length - 1
        );
      } else if (isDraggingDown) {
        // 向下拖拽并放置在下方，目标索引加1，但不超过数组长度
        adjustedEndIndex = Math.min(
          endIndex + 1,
          this.editorElements.length - 1
        );
      }
    } else if (placement === "above") {
      if (isDraggingDown) {
        // 向下拖拽并放置在上方，目标位置就是目标索引（无需调整）
        adjustedEndIndex = endIndex;
      } else if (isDraggingUp) {
        // 向上拖拽并放置在上方，直接使用目标索引
        adjustedEndIndex = endIndex;
      }
    }

    // 打印更详细的调试信息
    console.debug(
      `Reordering: start=${startIndex}, end=${endIndex}, adjusted=${adjustedEndIndex}, placement=${placement}, direction=${
        isDraggingUp ? "up" : isDraggingDown ? "down" : "same"
      }`
    );

    // 临时保存要移动的元素
    const elementToMove = this.editorElements[startIndex];

    // 从数组中移除元素
    this.editorElements.splice(startIndex, 1);

    // 在目标位置插入元素
    this.editorElements.splice(adjustedEndIndex, 0, elementToMove);

    // 更新画布排序
    this.updateCanvasOrder();

    // 选中移动后的元素
    const movedElement = this.editorElements[adjustedEndIndex];
    if (movedElement) {
      this.setSelectedElement(movedElement);
    }

    // 保存更改
    this.saveChange();

    // 记录性能指标
    const endTime = performance.now();
    if (endTime - startTime > 50) {
      console.debug(
        `[Performance] Direct element reorder took ${Math.round(
          endTime - startTime
        )}ms`
      );
    }

    // 返回移动信息
    return {
      sourceIndex: startIndex,
      targetIndex: adjustedEndIndex,
      element: movedElement,
      placement: placement,
    };
  }

  updateCanvasOrder() {
    this.elementManager.updateCanvasOrder();
  }

  /**
   * 根据轨道顺序更新Canvas上的元素显示顺序
   * 确保元素在Canvas上的显示顺序与时间线轨道的顺序一致
   * 轨道从上到下，同一轨道内的元素从左到右排序
   */
  updateCanvasOrderByTrackOrder() {
    // 获取按轨道顺序排序的所有元素
    const orderedElements = this.trackManager.getAllElementsInDisplayOrder();

    // 如果没有元素，直接返回
    if (orderedElements.length === 0) return;

    // 创建一个新的editorElements数组，保持原有的元素但按照新的顺序排列
    // 注意：我们需要保留不在轨道中的元素（如果有的话）
    const newEditorElements: EditorElement[] = [];

    // 首先添加所有按轨道顺序排序的元素
    orderedElements.forEach((element) => {
      newEditorElements.push(element);
    });

    // 然后添加所有不在轨道中的元素（如果有的话）
    this.editorElements.forEach((element) => {
      // 检查元素是否已经在新数组中
      const isAlreadyIncluded = newEditorElements.some(
        (el) => el.id === element.id
      );
      if (!isAlreadyIncluded) {
        newEditorElements.push(element);
      }
    });

    // 更新editorElements数组
    this.editorElements = newEditorElements;

    // 更新Canvas上的元素顺序
    this.elementManager.updateCanvasOrder();
  }

  moveElement(element: any, direction: "up" | "down" | "top" | "bottom") {
    this.elementManager.moveElement(element, direction);
  }

  // 根据拖拽垂直距离交换元素位置
  swapElementsByDrag(elementId: string, deltaY: number) {
    // 获取元素当前索引
    const currentIndex = this.editorElements.findIndex(
      (el) => el.id === elementId
    );
    if (currentIndex === -1) return; // 元素不存在

    // 改进的敏感度计算，使拖拽更加自然
    // 使用非线性更平滑的敏感度更新
    const dragSensitivity = Math.min(0.12, 0.06 + Math.abs(deltaY) * 0.0003);
    const moveDistance =
      Math.sign(deltaY) *
      Math.min(
        Math.floor(Math.abs(deltaY) * dragSensitivity),
        this.editorElements.length - 1
      );

    if (moveDistance === 0) return; // 移动距离不足，不进行交换

    // 计算目标位置索引 - 应用非线性映射使移动更自然
    let targetIndex = currentIndex + moveDistance;

    // 确保目标索引在有效范围内
    targetIndex = Math.max(
      0,
      Math.min(targetIndex, this.editorElements.length - 1)
    );

    // 如果目标索引等于当前索引，则不需要移动
    if (targetIndex === currentIndex) return;

    // 记录操作开始时间，用于性能监测
    const startTime = performance.now();

    // 使用现有的reorderElements方法来实现一致的元素重排序
    this.reorderElements(currentIndex, targetIndex);

    // 记录性能指标
    const endTime = performance.now();
    // 返回有关移动操作的信息，可用于动画
    return {
      sourceIndex: currentIndex,
      targetIndex: targetIndex,
      element: this.editorElements[targetIndex],
    };
  }

  flipElement(id: string, flipType: "horizontal" | "vertical") {
    const element = this.editorElements.find((el) => el.id === id);
    const fabricObject = element.fabricObject;
    if (element && element.fabricObject) {
      if (flipType === "horizontal") {
        fabricObject.set("flipX", !element.placement.flipX);

        element.placement = {
          ...element.placement,
          flipX: !element.placement.flipX,
        };
      } else {
        fabricObject.set("flipY", !element.placement.flipY);
        element.placement = {
          ...element.placement,
          flipY: !element.placement.flipY,
        };
      }

      this.canvas.requestRenderAll();
      this.saveChange();
    }
  }

  setMediaFilter(
    id: string,
    filterType: "brightness" | "contrast" | "saturation" | "hue" | "blur",
    value: number
  ) {
    this.elementManager.setMediaFilter(id, filterType, value);
    this.saveChange();
  }

  setMediaElementBorder(
    id: string,
    property: keyof BorderStyle,
    value: string | number
  ) {
    const element = this.editorElements.find((el) => el.id === id) as
      | ImageEditorElement
      | VideoEditorElement;
    const mediaObject = element.fabricObject;

    if (element && ("image" === element.type || "video" === element.type)) {
      element.properties.border = {
        ...element.properties.border,
        [property]: value,
      };
      //@ts-ignore
      mediaObject.set("imageBorderColor", element.properties.border.color);
      //@ts-ignore
      mediaObject.set("borderWidth", element.properties.border.width);
      //@ts-ignore
      mediaObject.set("borderStyle", element.properties.border.style);
      //@ts-ignore
      mediaObject.set("borderRadius", element.properties.border.borderRadius);
    }
    this.canvas.requestRenderAll();
    this.saveChange();
  }

  /**
   * Gets the EditorElement corresponding to the currently active object on the canvas
   * @returns The EditorElement if found, null otherwise
   */
  getActiveElement(): EditorElement | null {
    if (!this.canvas) return null;
    const activeObject = this.canvas.getActiveObject();
    if (!activeObject) return null;

    return (
      this.editorElements.find(
        (element) => element.fabricObject === activeObject
      ) || null
    );
  }

  isActiveElement() {
    return this.canvas.getActiveObject() !== null;
  }

  destroy() {
    // Cancel any ongoing animation frame
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = undefined;
    }

    // Stop and remove all media elements
    this.editorElements.forEach((element) => {
      if (element.type === "video" || element.type === "audio") {
        const media = document.getElementById(
          element.properties.elementId
        ) as HTMLMediaElement;
        if (media) {
          media.pause();
          media.remove();
        }
      }
    });

    // Stop and properly destroy the animation timeline
    if (this.animationTimeLine) {
      this.animationTimeLine.pause();
      anime.remove(this.animationTimeLine);
      this.animationTimeLine = null;
    }

    // Destroy managers
    if (this.animationManager) {
      this.animationManager.destroy();
    }
    if (this.elementManager) {
      this.elementManager.destroy();
    }
    // Clear arrays and reset properties
    this.videos = [];
    this.images = [];
    this.audios = [];
    this.editorElements = [];
    this.animations = [];
    this.selectedElement = null;
    // Clear the canvas
    if (this.canvas) {
      this.canvas.clear();
      this.canvas.dispose();
      this.canvas = null;
    }

    // Remove any event listeners (if any were added)
    // For example:
    // window.removeEventListener('resize', this.handleResize);

    // Reset other properties
    this.playing = false;
    this.currentKeyFrame = 0;
    this.startedTime = 0;
    this.startedTimePlay = 0;
  }

  /**
   * 保存更改，使用防抖函数延迟保存
   * @param actionType 操作类型，默认为"其他操作"
   */
  saveChange(actionType?: HistoryActionType) {
    // 如果正在导入状态或正在应用历史记录状态，不保存历史记录
    if (this._isImporting || this.historyManager.isApplyingHistoryState) return;
    this.debouncedSaveChange(actionType);
  }

  /**
   * 保存当前状态到历史记录
   * @param actionType 操作类型，默认为"其他操作"
   */
  saveToHistory(actionType?: HistoryActionType) {
    this.historyManager.saveToHistory(actionType);
  }

  /**
   * 开始操作分组，将连续的操作视为一个操作
   * @param actionType 操作类型
   */
  startHistoryGroup(actionType: HistoryActionType) {
    this.historyManager.startGrouping(actionType);
  }

  /**
   * 结束操作分组
   */
  endHistoryGroup() {
    this.historyManager.endGrouping();
  }

  /**
   * 撤销操作
   * @returns 是否成功撤销
   */
  undo(): boolean {
    return this.historyManager.undo();
  }

  /**
   * 重做操作
   * @returns 是否成功重做
   */
  redo(): boolean {
    return this.historyManager.redo();
  }

  /**
   * 获取可以撤销的操作类型
   * @returns 可以撤销的操作类型，如果没有则返回null
   */
  getUndoActionType(): HistoryActionType | null {
    return this.historyManager.getUndoActionType();
  }

  /**
   * 获取可以重做的操作类型
   * @returns 可以重做的操作类型，如果没有则返回null
   */
  getRedoActionType(): HistoryActionType | null {
    return this.historyManager.getRedoActionType();
  }

  // Timeline panning methods
  startTimelinePan(startX: number) {
    this.timelinePan.isDragging = true;
    this.timelinePan.startX = startX;
  }

  updateTimelinePan(currentX: number) {
    console.log("updateTimelinePan", this.timelinePan.offsetX);
    if (!this.timelinePan.isDragging) return;

    // Calculate the delta movement
    const deltaX = currentX - this.timelinePan.startX;

    // Calculate a pan speed factor that scales with timeline length
    const basePanFactor = 6.0;
    const baseTimelineLength = 30 * 1000; // 30 seconds in ms
    const panFactor =
      basePanFactor * (this.timelineDisplayDuration / baseTimelineLength);

    // Update the offset (negative deltaX because we want to move the timeline in the opposite direction)
    // Apply the scaling factor to maintain consistent feel regardless of timeline length
    this.timelinePan.offsetX += -deltaX * panFactor;

    // Clamp the offset to prevent panning too far
    // Never allow negative offset (prevent showing time before 0)
    // Positive offset means panning left (showing later parts of the timeline)
    // Allow scrolling up to maxTime when zoomed in
    const maxAllowedTime = Math.max(this.timelineDisplayDuration, this.maxTime);
    const maxOffset = Math.max(
      this.timelineDisplayDuration * 0.5,
      maxAllowedTime - this.timelineDisplayDuration
    );

    this.timelinePan.offsetX = Math.max(
      0, // Prevent negative offset (scrolling right beyond start)
      Math.min(maxOffset, this.timelinePan.offsetX)
    );

    // Update the start position for the next move
    this.timelinePan.startX = currentX;
  }

  endTimelinePan() {
    this.timelinePan.isDragging = false;
  }

  resetTimelinePan() {
    this.timelinePan.offsetX = 0;
  }

  // 用于节流滚动事件的变量
  private _lastWheelTime: number = 0;
  private _wheelAnimationFrame: number | null = null;
  private _pendingDeltaX: number = 0;
  private readonly _wheelThrottleInterval: number = 16; // 约60fps

  // Handle wheel scrolling for timeline with improved performance
  handleTimelineWheel(deltaX: number) {
    // 累积滚动量
    this._pendingDeltaX += deltaX;

    // 如果已经有一个动画帧在等待，不再请求新的
    if (this._wheelAnimationFrame !== null) return;

    // 检查是否需要节流
    const now = performance.now();
    const timeSinceLastWheel = now - this._lastWheelTime;

    if (timeSinceLastWheel < this._wheelThrottleInterval) {
      // 需要节流，使用requestAnimationFrame
      this._wheelAnimationFrame = requestAnimationFrame(() =>
        this._processWheelScroll()
      );
      return;
    }

    // 不需要节流，直接处理
    this._processWheelScroll();
  }

  // 实际处理滚动的内部方法
  private _processWheelScroll() {
    // 重置动画帧引用
    this._wheelAnimationFrame = null;
    this._lastWheelTime = performance.now();

    // 如果没有待处理的滚动量，直接返回
    if (this._pendingDeltaX === 0) return;

    // 计算滚动速度因子，根据时间线长度动态调整
    const baseScrollFactor = 15.0;
    const baseTimelineLength = 30 * 1000; // 30 seconds in ms
    const scrollFactor =
      baseScrollFactor * (this.timelineDisplayDuration / baseTimelineLength);

    // 更新偏移量
    this.timelinePan.offsetX += this._pendingDeltaX * scrollFactor;

    // 限制偏移量范围
    const maxAllowedTime = Math.max(this.timelineDisplayDuration, this.maxTime);
    const maxOffset = Math.max(
      this.timelineDisplayDuration * 0.5,
      maxAllowedTime - this.timelineDisplayDuration
    );

    this.timelinePan.offsetX = Math.max(
      0, // 防止负偏移（向右滚动超过起始位置）
      Math.min(maxOffset, this.timelinePan.offsetX)
    );

    // 重置累积的滚动量
    this._pendingDeltaX = 0;
  }

  // Set timeline pan offset directly (for scrollbar)
  setTimelinePanOffset(offsetX: number) {
    // Clamp the offset to prevent scrolling too far
    // Allow scrolling up to maxTime when zoomed in
    const maxAllowedTime = Math.max(this.timelineDisplayDuration, this.maxTime);
    const maxOffset = Math.max(
      this.timelineDisplayDuration * 0.5,
      maxAllowedTime - this.timelineDisplayDuration
    );

    this.timelinePan.offsetX = Math.max(
      0, // Prevent negative offset (scrolling right beyond start)
      Math.min(maxOffset, offsetX)
    );
  }

  /**
   * 将前端动画转换为后端FFmpeg可识别的transition格式
   * @param elementId 元素ID
   * @returns transition对象
   */
  private convertAnimationsToTransitions(elementId: string) {
    const elementAnimations = this.animations.filter(
      (animation) => animation.targetId === elementId
    );

    const transition: {
      in?: string;
      out?: string;
      inDuration?: number;
      outDuration?: number;
      // 保留duration字段以向后兼容，但优先使用inDuration和outDuration
      duration?: number;
    } = {};

    // 处理入场动画
    const inAnimation = elementAnimations.find((anim) => anim.group === "in");
    if (inAnimation) {
      transition.in = this.mapAnimationTypeToXfadeTransition(
        inAnimation.type,
        inAnimation.properties
      );
      transition.inDuration = inAnimation.duration / 1000; // 转换为秒
      console.log(
        `🎬 入场动画 - 元素 ${elementId}: 类型=${inAnimation.type}, 时间=${transition.inDuration}秒`
      );
    } else {
      // 如果没有入场动画，设置为none以禁用默认的xfade效果
      transition.in = "none";
    }

    // 处理出场动画
    const outAnimation = elementAnimations.find((anim) => anim.group === "out");
    if (outAnimation) {
      transition.out = this.mapAnimationTypeToXfadeTransition(
        outAnimation.type,
        outAnimation.properties
      );
      transition.outDuration = outAnimation.duration / 1000; // 转换为秒
      console.log(
        `🎬 出场动画 - 元素 ${elementId}: 类型=${outAnimation.type}, 时间=${transition.outDuration}秒`
      );
    } else {
      // 如果没有出场动画，设置为none以禁用默认的xfade效果
      transition.out = "none";
    }

    // 为了向后兼容，如果只有一个动画，也设置duration字段
    if (transition.inDuration && !transition.outDuration) {
      transition.duration = transition.inDuration;
    } else if (transition.outDuration && !transition.inDuration) {
      transition.duration = transition.outDuration;
    } else if (transition.inDuration && transition.outDuration) {
      // 如果两个都有，使用入场动画的时间作为默认值（向后兼容）
      transition.duration = transition.inDuration;
    }

    return transition;
  }

  /**
   * 将前端动画类型映射到FFmpeg xfade transition类型
   * @param animationType 前端动画类型
   * @param properties 动画属性
   * @returns FFmpeg xfade transition类型
   */
  private mapAnimationTypeToXfadeTransition(
    animationType: string,
    properties?: any
  ): string {
    switch (animationType) {
      case "fadeIn":
      case "fadeOut":
        return "fade";

      case "slideIn":
      case "slideOut":
        // 检查是否使用clipPath来区分slide和wipe动画
        const useClipPath = properties?.useClipPath;
        const direction = properties?.direction;

        if (useClipPath) {
          // Wipe动画：使用wipe系列效果
          switch (direction) {
            case "left":
              return "wiperight";
            case "right":
              return "wipeleft";
            case "top":
              return "wipedown";
            case "bottom":
              return "wipeup";
            default:
              return "fade"; // 默认使用fade
          }
        } else {
          // Slide动画：使用slide系列效果
          switch (direction) {
            case "left":
              return "slideright";
            case "right":
              return "slideleft";
            case "top":
              return "slidedown";
            case "bottom":
              return "slideup";
            default:
              return "fade"; // 默认使用fade
          }
        }

      case "breathe":
        return "fade"; // 呼吸效果使用fade

      case "rotate":
        return "circleopen"; // 旋转效果使用circleopen

      case "bounce":
      case "shake":
        return "fade"; // 弹跳和抖动效果使用fade

      case "flash":
        return "fade"; // 闪烁效果使用fade

      case "zoom":
      case "zoomIn":
      case "zoomOut":
        return "circleopen"; // 缩放效果使用circleopen

      default:
        return "fade"; // 默认使用fade
    }
  }

  exportCanvasState() {
    if (!this.canvas) {
      return null;
    }

    // Create a clean copy of canvas state without fabric.js objects
    const canvasState = {
      backgroundColor: this.backgroundColor,
      width: this.canvasWidth,
      height: this.canvasHeight,
      elements: this.editorElements.map(({ fabricObject, ...element }) => {
        // 为每个元素添加transition信息（仅用于后端视频生成）
        const transition = this.convertAnimationsToTransitions(element.id);

        // 调试日志：检查动画转换
        const elementAnimations = this.animations.filter(
          (animation) => animation.targetId === element.id
        );
        if (elementAnimations.length > 0) {
          console.log(`🔍 动画调试 - 元素 ${element.id}:`);
          console.log("原始动画:", elementAnimations);
          console.log("转换后的transition:", transition);
        }

        return {
          ...element,
          // 添加transition属性，专门用于后端FFmpeg处理
          // 注意：这不会替代animations数组，而是作为后端专用的简化数据
          transition,
          placement: element.placement
            ? {
                ...element.placement,
                x: element.placement.x
                  ? Number(element.placement.x.toFixed(2))
                  : 0,
                y: element.placement.y
                  ? Number(element.placement.y.toFixed(2))
                  : 0,
                width: element.placement.width
                  ? Number(element.placement.width.toFixed(2))
                  : 0,
                height: element.placement.height
                  ? Number(element.placement.height.toFixed(2))
                  : 0,
              }
            : null,
          timeFrame: {
            start: Number(element.timeFrame.start.toFixed(2)),
            end: Number(element.timeFrame.end.toFixed(2)),
          },
        };
      }),
      animations: this.animations,
      captions: this.captions,
      // 添加全局字幕样式
      globalCaptionStyle: this.captionManager.globalCaptionStyle,
      // 添加轨道信息
      tracks: this.trackManager.tracks,
      // 添加默认轨道信息
      // defaultTracks: this.trackManager.defaultTracks,
    };
    console.log("导出的画布状态（包含transition信息）:", canvasState);
    return JSON.stringify(canvasState);
  }

  importCanvasState(jsonState: string) {
    try {
      // 设置导入标志，防止在导入过程中保存历史记录
      this._isImporting = true;

      // 设置加载状态为开始加载
      this.setLoading(true, "正在加载项目数据...");

      // 清空历史记录，避免加载新状态时与旧历史记录混淆
      this.historyManager.clearHistory();

      const canvasData = JSON.parse(jsonState);

      // 1. 初始化画布
      this.setCanvasSize(canvasData.width, canvasData.height);
      this.setBackgroundColor(canvasData.backgroundColor);
      this.setEditorElements([]);
      this.animations = [];

      // 清空轨道并重新初始化
      this.trackManager.tracks = [];
      this.trackManager.defaultTracks = {
        media: "",
        audio: "",
        text: "",
        caption: "",
      };

      // Import captions if available
      if (canvasData.captions) {
        this.captionManager.setCaptions(canvasData.captions);
        this.captions = this.captionManager.captions;
      }

      // Import global caption style if available
      if (canvasData.globalCaptionStyle) {
        this.captionManager.globalCaptionStyle = canvasData.globalCaptionStyle;
      }

      // 导入轨道信息（如果有）
      if (canvasData.tracks) {
        this.trackManager.tracks = canvasData.tracks;
      }

      // 导入默认轨道信息（如果有）
      if (canvasData.defaultTracks) {
        this.trackManager.defaultTracks = canvasData.defaultTracks;
      }

      // 2. 创建Promise化的元素创建函数
      const createElementAsync = (element: any): Promise<void> => {
        return new Promise((resolve) => {
          const elementCreators = {
            text: () => {
              this.setEditorElements([element, ...this.editorElements]);
              this.elementManager.addElement(element);
              // this.addText({
              //   id: element.id,
              //   text: element.properties.text,
              //   fontSize: element.properties.fontSize,
              //   fontWeight: element.properties.fontWeight,
              //   backgroundColor: element.properties.backgroundColor,
              // });
              resolve();
            },
            shape: () => {
              // 添加形状元素处理
              this.setEditorElements([element, ...this.editorElements]);
              this.elementManager.addElement(element);
              resolve();
            },
            image: () => {
              const imageElement = document.createElement("img");
              imageElement.src = element.properties.src;
              imageElement.id = `image-${element.id}`;
              imageElement.style.display = "none";
              document.body.appendChild(imageElement);
              imageElement.onload = () => {
                this.setEditorElements([element, ...this.editorElements]);
                this.elementManager.addElement(element);
                resolve();
              };
              imageElement.onerror = () => resolve(); // Handle load errors gracefully
            },
            video: () => {
              const videoElement = document.createElement("video");
              videoElement.style.display = "none";
              videoElement.src = element.properties.src;
              videoElement.id = `video-${element.id}`;
              videoElement.onloadeddata = () => {
                this.setEditorElements([element, ...this.editorElements]);
                this.elementManager.addElement(element);
                resolve();
              };
              videoElement.onerror = () => resolve(); // Handle load errors gracefully
              document.body.appendChild(videoElement);
            },
            audio: () => {
              const audio = document.createElement("audio");
              audio.src = element.properties.src;
              audio.id = `audio-${element.id}`;
              audio.onloadeddata = () => {
                // 在导入状态时，不应该使用addAudio方法，因为它会创建新的轨道
                // 而是直接添加元素到editorElements数组中
                this.setEditorElements([element, ...this.editorElements]);
                // 不需要调用elementManager.addElement，因为音频元素不需要在画布上显示
                resolve();
              };
              audio.onerror = () => resolve(); // Handle load errors gracefully
              document.body.appendChild(audio);
            },
          };

          const creator = elementCreators[element.type];

          if (creator) {
            creator();
          } else {
            resolve();
          }
        });
      };

      // 3. 按顺序导入所有元素
      const importElements = async (): Promise<void> => {
        // 更新加载消息
        this.setLoading(true, "Loading elements...");

        for (const element of [...canvasData.elements].reverse()) {
          // 确保模板元素有必需的默认属性
          const elementWithDefaults = {
            ...element,
            locked: element.locked ?? false, // 默认不锁定
            opacity: element.opacity ?? 1, // 默认完全不透明
          };
          await createElementAsync(elementWithDefaults);
        }

        // 4. 所有媒体加载完成后，更新元素属性和动画
        // 使用批量更新来避免多次触发状态变化
        const updatedElements = this.editorElements.map((newElement) => {
          const originalElement = canvasData.elements.find(
            (element: any) => element.id === newElement.id
          );
          if (newElement && originalElement) {
            return {
              ...newElement,
              placement: originalElement.placement,
              timeFrame: originalElement.timeFrame,
              properties: {
                ...newElement.properties,
                ...originalElement.properties,
              },
              // 保留轨道ID（如果有）
              trackId: originalElement.trackId,
              // 确保关键属性有默认值
              locked: originalElement.locked ?? newElement.locked ?? false,
              opacity: originalElement.opacity ?? newElement.opacity ?? 1,
            };
          }
          return newElement;
        });

        // 批量更新所有元素
        this.setEditorElements(updatedElements);

        // 更新加载消息
        this.setLoading(true, "Loading animations...");

        // 更新画布动画
        if (canvasData.animations) {
          this.animations = canvasData.animations;
          this.refreshAnimations();
        }

        // 更新加载消息
        this.setLoading(true, "Loading tracks...");

        // 处理轨道和元素的关联关系
        if (!canvasData.tracks) {
          // 如果没有导入轨道信息，则重新初始化轨道并分配元素
          // 但不要创建新轨道，只使用现有元素的trackId

          // 首先清空现有轨道
          this.trackManager.tracks = [];

          // 收集所有元素的trackId
          const trackIds = new Set<string>();
          this.editorElements.forEach((element) => {
            if (element.trackId) {
              trackIds.add(element.trackId);
            }
          });

          // 为每个trackId创建一个轨道
          trackIds.forEach((trackId) => {
            // 找出这个trackId对应的元素
            const elementsWithTrackId = this.editorElements.filter(
              (element) => element.trackId === trackId
            );

            if (elementsWithTrackId.length > 0) {
              // 确定轨道类型
              let trackType: TrackType;
              const firstElement = elementsWithTrackId[0];

              if (
                firstElement.type === "image" ||
                firstElement.type === "video"
              ) {
                trackType = "media";
              } else {
                trackType = firstElement.type as TrackType;
              }

              // 创建轨道
              const track: Track = {
                id: trackId,
                name: `${
                  trackType.charAt(0).toUpperCase() + trackType.slice(1)
                } Track`,
                type: trackType,
                elementIds: elementsWithTrackId.map((element) => element.id),
                isVisible: true,
                isLocked: false,
              };

              this.trackManager.tracks.push(track);

              // 更新默认轨道
              if (!this.trackManager.defaultTracks[trackType]) {
                this.trackManager.defaultTracks[trackType] = trackId;
              }
            }
          });
        } else {
          // 确保元素与轨道的关联关系正确
          this.editorElements.forEach((element) => {
            if (element.trackId) {
              // 确保元素在对应的轨道中
              const track = this.trackManager.tracks.find(
                (t) => t.id === element.trackId
              );
              if (track && !track.elementIds.includes(element.id)) {
                track.elementIds.push(element.id);
              }
            }
          });
        }

        // 刷新元素显示
        this.refreshElements();

        // 清理无效的元素ID并删除空轨道
        this.trackManager.cleanupInvalidElementIds();

        // 根据轨道顺序更新Canvas上的元素显示顺序
        this.updateCanvasOrderByTrackOrder();

        // 更新加载消息
        this.setLoading(true, "Loading timeline...");

        // 更新最大时间
        this.updateMaxTime();

        // Auto fit timeline to show all imported elements
        this.fitTimelineToContent();
      };

      // 开始导入过程
      importElements().then(() => {
        // 重置导入标志
        this._isImporting = false;

        // 导入完成后，初始化历史记录
        // 这样第一个历史记录状态就是导入后的初始状态
        this.historyManager.initHistory();

        // 导入完成后，设置加载状态为完成
        // 使用更长的延迟确保所有异步操作和状态更新完成
        setTimeout(() => {
          // 确保所有元素都已正确加载和初始化
          const allElementsLoaded = this.editorElements.every((element) => {
            if (element.type === "text") {
              // 检查文字元素的关键属性是否已加载
              return (
                element.properties &&
                element.properties.fontSize !== undefined &&
                element.properties.fontColor !== undefined
              );
            }
            return true; // 其他类型的元素
          });

          if (allElementsLoaded) {
            this.setLoading(false, "");
            console.log("所有元素加载完成，UI可以安全渲染");
          } else {
            // 如果还有元素未完全加载，再等待一段时间
            setTimeout(() => {
              this.setLoading(false, "");
            }, 300);
          }
        }, 800); // 增加延迟时间，确保所有异步操作完成
      });
      return true;
    } catch (error) {
      console.error("Error importing canvas state:", error);
      // 发生错误时，也需要重置导入标志和加载状态
      this._isImporting = false;
      this.setLoading(false, "");
      return false;
    }
  }

  saveToLocalStorage() {
    try {
      // 在保存前清理无效的元素ID
      const removedCount = this.trackManager.cleanupInvalidElementIds();
      if (removedCount > 0) {
        console.log(`已从轨道中清理 ${removedCount} 个无效的元素ID`);
      }

      // 在保存前删除空轨道
      this.trackManager.removeEmptyTracks();

      // 导出状态并保存到本地存储
      const state = this.exportCanvasState();
      localStorage.setItem(this.autoSaveKey, state);
    } catch (error) {
      console.error("Failed to save canvas state to localStorage:", error);
    }
  }

  loadFromLocalStorage() {
    // 设置加载状态为开始加载
    this.setLoading(true, "正在从本地存储加载数据...");

    const savedState = localStorage.getItem(this.autoSaveKey);
    if (savedState) {
      console.log("🔄 开始从本地存储加载数据");
      const result = this.importCanvasState(savedState);

      // 确保更新最大时间
      this.updateMaxTime();

      console.log("✅ 本地存储数据加载完成，结果:", result);
      return result;
    }

    console.log("ℹ️ 没有找到本地存储的数据");
    // 如果没有保存的状态，也需要重置加载状态
    this.setLoading(false, "");
    return false;
  }

  /**
   * 创建一个防抖函数
   * @param func 要执行的函数
   * @param wait 等待时间（毫秒）
   * @returns 防抖函数
   */
  private debounce<T extends (...args: any[]) => any>(func: T, wait: number) {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  /**
   * 设置加载状态
   * @param isLoading 是否正在加载
   * @param message 加载消息
   */
  setLoading(isLoading: boolean, message: string = "") {
    this.isLoading = isLoading;
    this.loadingMessage = message;
  }

  /**
   * 从动画列表中移除指定元素组的动画。
   * @param elementId 移除动画的元素ID
   * @param group 要移除的动画组名称
   * @returns 无返回值。
   */
  removeAnimationByGroup(elementId: string, group: string) {
    console.log("removeAnimationByGroup", elementId, group);
    // Find and remove all animations matching both elementId and group
    this.animations = this.animations.filter(
      (animation) =>
        !(animation.targetId === elementId && animation.group === group)
    );
    console.log("this.animations", this.animations);
    // Get the element to update
    const element = this.editorElements.find(
      (element) => element.id === elementId
    );

    // Refresh animations and update the element
    this.animationManager.refreshAnimations();
    if (element) {
      this.updateEditorElement(element);
      this.canvas.renderAll();
    }
  }

  async exportVideo(format: string = "mp4") {
    console.log("exportVideo", format);
    const canvasState = this.exportCanvasState();
    console.log("canvasState", canvasState);
    const response = await fetch("http://localhost:8080/api/generateVideo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: canvasState,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to generate video");
    }

    // 解析响应并返回数据对象
    const responseData = await response.json();
    return responseData;
  }

  async hasAudioTrack(videoElement: any) {
    try {
      // 方法1: 检查音频轨道
      if (videoElement.audioTracks && videoElement.audioTracks.length > 0) {
        return true;
      }

      // 方法2: 检查音频解码字节数
      if (videoElement.webkitAudioDecodedByteCount > 0) {
        return true;
      }

      // 方法3: Firefox特有的检测方法
      if (videoElement.mozHasAudio) {
        return true;
      }

      // 方法4: 通过MediaSource检测
      if (videoElement.captureStream) {
        const stream = videoElement.captureStream();
        const audioTracks = stream.getAudioTracks();
        if (audioTracks.length > 0) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.warn("Error checking for audio tracks:", error);
      return false;
    }
  }

  setProjectName(name: string) {
    this.projectName = name;
    this.saveChange();
  }

  /**
   * 设置编辑模式
   * @param mode 编辑模式：'move'或'hand'
   */
  setEditMode(mode: "move" | "hand") {
    // 如果模式没有变化，不做任何操作
    if (this.editMode === mode) return;

    // 保存旧模式，用于日志记录
    const oldMode = this.editMode;
    console.log(`切换编辑模式: ${oldMode} -> ${mode}`);

    this.editMode = mode;

    if (!this.canvas) return;

    // 如果切换到手工具模式，禁用所有对象的可选择性和可移动性
    if (mode === "hand") {
      this.canvas.discardActiveObject();
      this.setSelectedElement(null);

      // 禁用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = false;
        obj.evented = false; // 禁止所有事件交互
      });

      this.canvas.requestRenderAll();
    } else {
      // 当切换回移动模式时，启用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = true;
        obj.evented = true; // 恢复事件交互
      });

      this.canvas.requestRenderAll();
    }
  }

  /**
   * 更新画布缩放值
   * @param scale 新的缩放值
   * @param translation 新的平移值（可选）
   */
  updateCanvasScale(scale: number, translation?: { x: number; y: number }) {
    // 确保缩放值在有效范围内
    const minScale = 0.1;
    const maxScale = 2;
    this.canvasScale = Math.max(minScale, Math.min(maxScale, scale));

    // 如果提供了平移值，则更新
    if (translation) {
      this.canvasTranslation = translation;
    }
  }

  /**
   * 放大画布
   * @param step 缩放步长，默认为0.1
   */
  zoomIn(step: number = 0.1) {
    const newScale = this.canvasScale + step;
    this.updateCanvasScale(newScale);
    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 缩小画布
   * @param step 缩放步长，默认为0.1
   */
  zoomOut(step: number = 0.1) {
    const newScale = this.canvasScale - step;
    this.updateCanvasScale(newScale);
    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 重置画布缩放
   */
  resetZoom() {
    // 获取窗口尺寸
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 计算适合窗口的缩放值，同时保持宽高比
    const scaleX = (windowWidth * 0.7) / 1920;
    const scaleY = (windowHeight * 0.7) / 1080;
    const scale = Math.min(scaleX, scaleY);

    // 居中画布
    const x = windowWidth / 2;
    const y = (windowHeight - 260) / 2; // 考虑UI元素（260px）

    this.updateCanvasScale(scale, { x, y });
    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  // Format time string to "00:00:00" format
  formatTimeString(timeStr: string): string {
    return this.captionManager.formatTimeString(timeStr);
  }

  // Forward caption methods to captionManager
  addCaption() {
    this.captionManager.addCaption();
    this.captions = this.captionManager.captions;
  }

  deleteCaption(id: string) {
    this.captionManager.deleteCaption(id);
    this.captions = this.captionManager.captions;
  }

  selectCaption(id: string) {
    // 选中字幕时，取消时间线元素的选中状态
    this.selectedElement = null;

    this.captionManager.selectCaption(id);
    this.captions = this.captionManager.captions;
  }

  updateCaption(id: string, field: keyof Caption, value: string) {
    this.captionManager.updateCaption(id, field, value);
    this.captions = this.captionManager.captions;
  }

  updateCaptionTimeFrame(id: string, startTime: string, endTime: string) {
    // 验证时间格式并转换为毫秒进行比较
    const startTimeMs = this.captionManager.timeStringToMilliseconds(startTime);
    const endTimeMs = this.captionManager.timeStringToMilliseconds(endTime);

    // 最终验证：确保startTime < endTime
    if (startTimeMs >= endTimeMs) {
      console.warn(
        `updateCaptionTimeFrame: 检测到startTime >= endTime，字幕ID: ${id}`
      );
      console.warn(
        `startTime: ${startTime} (${startTimeMs}ms), endTime: ${endTime} (${endTimeMs}ms)`
      );
      // 如果时间错乱，不执行更新
      return;
    }

    this.captionManager.updateCaption(id, "startTime", startTime);
    this.captionManager.updateCaption(id, "endTime", endTime);
    this.captionManager.sortCaptionsByStartTime();
    this.captions = this.captionManager.captions;
  }

  formatCaptionTime(timeStr: string): string {
    return this.captionManager.formatCaptionTime(timeStr);
  }

  addCaptionBetween(index: number) {
    this.captionManager.addCaptionBetween(index);
    this.captions = this.captionManager.captions;
  }

  mergeCaptions(index: number) {
    this.captionManager.mergeCaptions(index);
    this.captions = this.captionManager.captions;
  }

  deselectAllCaptions() {
    this.captionManager.deselectAllCaptions();
    this.captions = this.captionManager.captions;
  }

  exportCaptionsAsSRT(): string {
    return this.captionManager.exportCaptionsAsSRT();
  }

  exportAndDownloadCaptionsAsSRT() {
    this.captionManager.exportAndDownloadCaptionsAsSRT();
  }

  updateGlobalCaptionStyle(
    styleProps: Partial<import("../types").CaptionStyle>
  ) {
    this.captionManager.updateGlobalCaptionStyle(styleProps);
  }

  getGlobalCaptionStyle(): import("../types").CaptionStyle {
    return this.captionManager.globalCaptionStyle;
  }

  /**
   * 调试字幕位置信息
   */
  debugCaptionPosition() {
    this.captionManager.debugCaptionPosition();
  }

  getSelectedCaption(): Caption | null {
    return this.captionManager.getSelectedCaption();
  }

  importCaptionsFromSRT(srtContent: string) {
    this.captionManager.importCaptionsFromSRT(srtContent);
    this.captions = this.captionManager.captions;
    this.saveChange();
  }

  clearAllCaptions() {
    this.captionManager.clearAllCaptions();
    this.captions = this.captionManager.captions;
    this.saveChange();
  }

  setTimelineDisplayDuration(duration: number) {
    this.timelineDisplayDuration = duration;
  }

  // Fit the timeline to show all content
  fitTimelineToContent() {
    // Find the maximum end time across all elements
    const maxEndTime = this.editorElements.reduce((max, element) => {
      return Math.max(max, element.timeFrame.end);
    }, 0);

    // Add a small margin (10%) for better visualization
    const margin = maxEndTime * 0.1;

    // Set the timeline display duration to fit all content
    // If there are no elements or they're all at time 0, use a reasonable default (10 seconds)
    const newDuration = maxEndTime > 0 ? maxEndTime + margin : 10000;

    // Update the timelineDisplayDuration
    this.setTimelineDisplayDuration(Math.min(newDuration, this.maxTime));

    // Reset the pan offset to view from the beginning
    this.resetTimelinePan();
  }

  /**
   * 根据传入的新顺序重新排列编辑器元素
   * @param newElementsOrder 新的元素顺序数组
   */
  reorderElementsByDrag(newElementsOrder: EditorElement[]) {
    this.editorElements = newElementsOrder;

    // 更新轨道中的元素顺序
    // 首先获取所有轨道
    const tracks = this.trackManager.tracks;

    // 对于每个轨道，检查其中的元素是否需要重新排序
    tracks.forEach((track) => {
      // 获取轨道中的元素
      const trackElements = this.trackManager.getElementsByTrackId(track.id);

      // 如果轨道中有多个元素，检查是否有重叠并修复
      if (trackElements.length > 1) {
        this.trackManager.fixTrackElementsOverlap(track.id);
      }
    });

    // 根据轨道顺序更新Canvas上的元素显示顺序
    this.updateCanvasOrderByTrackOrder();
    this.saveChange();
  }
}
