import { fabric } from "fabric";
import { EditorElement, EffecType } from "../types";

export const TextboxWithPadding = fabric.util.createClass(fabric.Textbox, {
  type: "text",
  splitByGrapheme: true,
  includeDefaultValues: true,
  intersectsWithFrame: true,

  toObject: function () {
    // 使用 fabric.util.object.extend 可能会导致性能问题，因为它会深拷贝对象
    // 对于简单属性，可以直接返回一个新对象
    return {
      ...this.callSuper("toObject"),
      backgroundColor: this.get("backgroundColor"),
      splitByGrapheme: this.get("splitByGrapheme"),
      rx: this.get("rx"),
      ry: this.get("ry"),
      padding: this.get("padding"),
    };
  },

  _getNonTransformedDimensions: function () {
    const dim = this.callSuper("_getNonTransformedDimensions");
    const padding = this.padding || 0;
    const doublePadding = padding * 2;

    const fontSize = this.fontSize || 0;
    const lineHeight = this.lineHeight || 1;
    const textHeight = fontSize * lineHeight;

    return {
      x: Math.max(dim.x + doublePadding, this.width || 0),
      y: Math.max(dim.y + doublePadding, textHeight + doublePadding),
    };
  },

  _renderBackground: function (ctx) {
    if (!this.backgroundColor) {
      return;
    }
    var dim = this._getNonTransformedDimensions();
    ctx.fillStyle = this.backgroundColor || "transparent";

    // 缓存常用值避免重复计算
    const rx = this.rx || 10;
    const ry = this.ry || 10;
    const x = -dim.x / 2;
    const y = -dim.y / 2;
    const w = dim.x;
    const h = dim.y;

    // 暂时保存当前上下文状态
    ctx.save();

    // 清除阴影设置,这样背景就不会有阴影效果
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // 使用 path2D 可以提升性能
    const path = new Path2D();

    // 绘制圆角矩形路径
    path.moveTo(x + rx, y);
    path.lineTo(x + w - rx, y);
    path.quadraticCurveTo(x + w, y, x + w, y + ry);
    path.lineTo(x + w, y + h - ry);
    path.quadraticCurveTo(x + w, y + h, x + w - rx, y + h);
    path.lineTo(x + rx, y + h);
    path.quadraticCurveTo(x, y + h, x, y + h - ry);
    path.lineTo(x, y + ry);
    path.quadraticCurveTo(x, y, x + rx, y);
    path.closePath();

    // 填充背景
    ctx.fill(path);

    // 只在有 strokeColor 时绘制边框
    if (this.strokeColor) {
      ctx.strokeStyle = this.strokeColor;
      ctx.stroke(path);
    }

    // 恢复上下文状态,这样文字还会保留阴影效果
    ctx.restore();
  },
});
// 创建基类
const CoverElementBase = fabric.util.createClass(fabric.Image, {
  type: "coverElement",
  customFilter: "none",
  disableCrop: false,
  cropWidth: 0,
  cropHeight: 0,

  brightness: 0,
  contrast: 0,
  saturation: 0,
  hue: 0,
  blur: 0,
  noise: 50,

  imageBorderColor: "transparent",
  borderWidth: 0,
  borderStyle: "solid",
  borderRadius: 0,
  imageFlipX: false,
  imageFlipY: false,

  _cachedClipPath: null,
  _lastWidth: 0,
  _lastHeight: 0,
  _lastRadius: 0,

  getOriginalSize() {
    // 获取原始图片/视频的尺寸
    if (this._element) {
      if (this._element instanceof HTMLImageElement) {
        return {
          width: this._element.naturalWidth || this._element.width,
          height: this._element.naturalHeight || this._element.height,
        };
      } else if (this._element instanceof HTMLVideoElement) {
        return {
          width: this._element.videoWidth || this._element.width,
          height: this._element.videoHeight || this._element.height,
        };
      }
    }
    // 如果无法获取原始尺寸，返回当前尺寸
    return {
      width: this.width || 100,
      height: this.height || 100,
    };
  },

  getCrop(
    image: { width: number; height: number },
    size: { width: number; height: number }
  ) {
    // 如果对象有设置裁剪参数，优先使用
    if (
      this.cropX !== undefined &&
      this.cropY !== undefined &&
      this.cropWidth &&
      this.cropHeight
    ) {
      return {
        cropX: this.cropX,
        cropY: this.cropY,
        cropWidth: this.cropWidth,
        cropHeight: this.cropHeight,
      };
    }

    // 否则使用原有的宽高比例计算逻辑
    const { width, height } = size;
    const aspectRatio = width / height;
    const imageRatio = image.width / image.height;

    let newWidth, newHeight;
    if (aspectRatio >= imageRatio) {
      newWidth = image.width;
      newHeight = image.width / aspectRatio;
    } else {
      newWidth = image.height * aspectRatio;
      newHeight = image.height;
    }

    return {
      cropX: (image.width - newWidth) / 2,
      cropY: (image.height - newHeight) / 2,
      cropWidth: newWidth,
      cropHeight: newHeight,
    };
  },

  _createClipPath() {
    const width = this.width;
    const height = this.height;
    const r = this.borderRadius;

    // 如果尺寸和圆角没有变化，使用缓存的路径
    if (
      this._cachedClipPath &&
      this._lastWidth === width &&
      this._lastHeight === height &&
      this._lastRadius === r
    ) {
      return this._cachedClipPath;
    }

    // 创建新路径
    const path = new Path2D();
    path.moveTo(-width / 2 + r, -height / 2);
    path.arcTo(width / 2, -height / 2, width / 2, height / 2, r);
    path.arcTo(width / 2, height / 2, -width / 2, height / 2, r);
    path.arcTo(-width / 2, height / 2, -width / 2, -height / 2, r);
    path.arcTo(-width / 2, -height / 2, width / 2, -height / 2, r);
    path.closePath();

    // 更新缓存
    this._cachedClipPath = path;
    this._lastWidth = width;
    this._lastHeight = height;
    this._lastRadius = r;

    return path;
  },

  getCompositeFilter() {
    const filters = [
      getFilterFromEffectType(this.customFilter),
      `brightness(${100 + (this.brightness || 0)}%)`,
      `contrast(${100 + (this.contrast || 0)}%)`,
      `saturate(${100 + (this.saturation || 0)}%)`,
      `hue-rotate(${this.hue || 0}deg)`,
      `blur(${this.blur || 0}px)`,
    ];

    return (
      filters.filter((f) => f !== "none" && !f.includes("NaN")).join(" ") ||
      "none"
    );
  },

  applyImageFilters() {
    if (this.canvas) {
      this.canvas.requestRenderAll();
    }
  },

  // 所有的 setter 方法
  setBrightness(value: number) {
    this.brightness = value;
    this.applyImageFilters();
  },

  setContrast(value: number) {
    this.contrast = value;
    this.applyImageFilters();
  },

  setSaturation(value: number) {
    this.saturation = value;
    this.applyImageFilters();
  },

  setHue(value: number) {
    this.hue = value;
    this.applyImageFilters();
  },

  setBlur(value: number) {
    this.blur = value;
    this.applyImageFilters();
  },

  setNoise(value: number) {
    this.noise = value;
    this.applyImageFilters();
  },

  setBorder(color: string, width: number, style: string = "solid") {
    if (!color) {
      console.error("Invalid border color");
      return;
    }
    this.imageBorderColor = color;
    this.borderWidth = width;
    this.borderStyle = style;
    this.canvas.requestRenderAll();
  },

  dispose() {
    this._cachedClipPath = null;
    this._lastWidth = 0;
    this._lastHeight = 0;
    this._lastRadius = 0;
    this.callSuper("dispose");
  },
});

// 修改 CoverImage
export const CoverImage = fabric.util.createClass(CoverElementBase, {
  type: "coverImage",

  initialize(element: HTMLImageElement, options: any) {
    options = {
      cropHeight: this.height,
      cropWidth: this.width,
      ...options,
    };
    this.imageFlipX = options.flipX;
    this.imageFlipY = options.flipY;
    this.callSuper("initialize", element, options);
    this.applyFilters();
  },

  _render(ctx: CanvasRenderingContext2D) {
    if (this.disableCrop) {
      return this.callSuper("_render", ctx);
    }

    const { width, height } = this;
    const originalSize = this.getOriginalSize();

    // 获取剪裁参数
    const { cropX, cropY, cropWidth, cropHeight } = this.getCrop(originalSize, {
      width: this.getScaledWidth(),
      height: this.getScaledHeight(),
    });

    ctx.save();

    // 处理翻转
    if (this.imageFlipX || this.imageFlipY) {
      if (this.imageFlipX) ctx.scale(-1, 1);
      if (this.imageFlipY) ctx.scale(1, -1);
    }

    // 使用缓存的裁剪路径
    const clipPath = this._createClipPath();
    ctx.clip(clipPath);

    // 应用滤镜并绘制图像
    ctx.filter = this.getCompositeFilter();

    // 正确的剪裁逻辑：
    // 1. 从原始图片的 (cropX, cropY) 位置开始
    // 2. 取 cropWidth x cropHeight 大小的区域
    // 3. 绘制到画布的整个显示区域
    ctx.drawImage(
      this._element,
      // 源图片的剪裁区域
      Math.max(cropX, 0), // sx: 源图片 x 坐标
      Math.max(cropY, 0), // sy: 源图片 y 坐标
      Math.max(1, cropWidth), // sWidth: 源图片宽度
      Math.max(1, cropHeight), // sHeight: 源图片高度
      // 目标画布区域
      -width / 2, // dx: 目标 x 坐标
      -height / 2, // dy: 目标 y 坐标
      width, // dWidth: 目标宽度
      height // dHeight: 目标高度
    );
    ctx.filter = "none";

    // 绘制边框（如果需要）
    if (this.borderWidth > 0) {
      ctx.strokeStyle = this.imageBorderColor;
      ctx.lineWidth = this.borderWidth;

      if (this.borderStyle === "dashed") {
        ctx.setLineDash([this.borderWidth * 2, this.borderWidth]);
      } else if (this.borderStyle === "dotted") {
        ctx.setLineDash([this.borderWidth, this.borderWidth]);
      }

      // 使用相同的路径绘制边框
      ctx.stroke(clipPath);
    }

    ctx.restore();
  },
});

// 修改 CoverVideo
export const CoverVideo = fabric.util.createClass(CoverElementBase, {
  type: "coverVideo",

  initialize(element: HTMLVideoElement, options: any) {
    options = options || {};
    options = Object.assign(
      {
        cropHeight: this.height,
        cropWidth: this.width,
      },
      options
    );
    this.callSuper("initialize", element, options);
  },

  _render(ctx: CanvasRenderingContext2D) {
    if (this.disableCrop) {
      return this.callSuper("_render", ctx);
    }

    const width = this.width;
    const height = this.height;
    const originalSize = this.getOriginalSize();

    // 获取剪裁参数
    const { cropX, cropY, cropWidth, cropHeight } = this.getCrop(originalSize, {
      width: this.getScaledWidth(),
      height: this.getScaledHeight(),
    });

    ctx.save();

    // 处理翻转
    if (this.imageFlipX || this.imageFlipY) {
      if (this.imageFlipX) ctx.scale(-1, 1);
      if (this.imageFlipY) ctx.scale(1, -1);
    }

    // 使用缓存的裁剪路径
    const clipPath = this._createClipPath();
    ctx.clip(clipPath);

    // 应用滤镜并绘制视频帧
    ctx.filter = this.getCompositeFilter();

    // 正确的视频剪裁逻辑：
    // 1. 从原始视频的 (cropX, cropY) 位置开始
    // 2. 取 cropWidth x cropHeight 大小的区域
    // 3. 绘制到画布的整个显示区域
    ctx.drawImage(
      this._element,
      // 源视频的剪裁区域
      Math.max(cropX, 0), // sx: 源视频 x 坐标
      Math.max(cropY, 0), // sy: 源视频 y 坐标
      Math.max(1, cropWidth), // sWidth: 源视频宽度
      Math.max(1, cropHeight), // sHeight: 源视频高度
      // 目标画布区域
      -width / 2, // dx: 目标 x 坐标
      -height / 2, // dy: 目标 y 坐标
      width, // dWidth: 目标宽度
      height // dHeight: 目标高度
    );
    ctx.filter = "none";

    // 绘制边框（如果需要）
    if (this.borderWidth > 0) {
      ctx.strokeStyle = this.imageBorderColor;
      ctx.lineWidth = this.borderWidth;

      if (this.borderStyle === "dashed") {
        ctx.setLineDash([this.borderWidth * 2, this.borderWidth]);
      } else if (this.borderStyle === "dotted") {
        ctx.setLineDash([this.borderWidth, this.borderWidth]);
      }

      // 使用相同的路径绘制边框
      ctx.stroke(clipPath);
    }

    ctx.restore();
  },
});

function getFilterFromEffectType(effectType: EffecType) {
  switch (effectType) {
    case "blackAndWhite":
      return "grayscale(100%)";
    case "sepia":
      return "sepia(100%)";
    case "invert":
      return "invert(100%)";
    case "saturate":
      return "saturate(100%)";
    case "retro":
      return "retro(0%)";
    default:
      return "none";
  }
}

declare module "fabric" {
  namespace fabric {
    class CoverVideo extends Image {
      type: "coverVideo";
      disableCrop: boolean;
      cropX: number;
      cropY: number;
      cropWidth: number;
      cropHeight: number;

      brightness: number;
      contrast: number;
      saturation: number;
      hue: number;
      blur: number;
      noise: number;
      borderColor: string;
      borderWidth: number;
      borderStyle: string;
    }
    class CoverImage extends Image {
      type: "coverImage";
      disableCrop: boolean;
      cropX: number;
      cropY: number;
      cropWidth: number;
      cropHeight: number;

      brightness: number;
      contrast: number;
      saturation: number;
      hue: number;
      blur: number;
      noise: number;
      borderColor: string;
      borderWidth: number;
      borderStyle: string;
    }
    class TextboxWithPadding extends Textbox {
      type: "coverImage";
      disableCrop: boolean;
      cropWidth: number;
      cropHeight: number;

      brightness: number;
      contrast: number;
      saturation: number;
      hue: number;
      blur: number;
      noise: number;
      borderColor: string;
      borderWidth: number;
      borderStyle: string;
    }
  }
}

fabric.CoverImage = CoverImage;
fabric.CoverVideo = CoverVideo;
fabric.TextboxWithPadding = TextboxWithPadding;

export class FabricUitls {
  static getClipMaskRect(editorElement: EditorElement, extraOffset: number) {
    const extraOffsetX = extraOffset / editorElement.placement.scaleX;
    const extraOffsetY = extraOffsetX / editorElement.placement.scaleY;
    const clipRectangle = new fabric.Rect({
      left: editorElement.placement.x - extraOffsetX,
      top: editorElement.placement.y - extraOffsetY,
      width: editorElement.placement.width + extraOffsetX * 2,
      height: editorElement.placement.height + extraOffsetY * 2,
      scaleX: editorElement.placement.scaleX,
      scaleY: editorElement.placement.scaleY,
      absolutePositioned: true,
      fill: "transparent",
      stroke: "transparent",
      opacity: 0.5,
      strokeWidth: 0,
    });
    return clipRectangle;
  }
}
